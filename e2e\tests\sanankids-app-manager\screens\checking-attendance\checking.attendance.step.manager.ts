import { Step } from 'gauge-ts';
import { CheckingAttendanceManagerScreen } from './checking.attendance.screen.manager';

export class CheckingAttendanceMnagerSteps {
  @Step('Mobile: Click button checkin on checking attendance manager screen')
  public async clickCheckIn() {
    const elem = await (
      await CheckingAttendanceManagerScreen.getInstance()
    ).buttonCheckIn();
    await elem.waitForDisplayed();
    await elem.click();
  }

  @Step('Mobile: Click button checkout on checking attendance manager screen')
  public async clickCheckOut() {
    const elem = await (
      await CheckingAttendanceManagerScreen.getInstance()
    ).buttonCheckOut();
    await elem.waitForDisplayed();
    await elem.click();
  }

  @Step('Mobile: Click button undo on checking attendance manager screen')
  public async clickUndo() {
    const elem = await (
      await CheckingAttendanceManagerScreen.getInstance()
    ).buttonUndo();
    await elem.waitForDisplayed();
    await elem.click();
  }

  @Step('Mobile: Click button undo all on checking attendance manager screen')
  public async clickUndoAll() {
    const elem = await (
      await CheckingAttendanceManagerScreen.getInstance()
    ).buttonUndoAll();
    await elem.waitForDisplayed();
    await elem.click();
  }
}