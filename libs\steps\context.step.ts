import {
  Omit<PERSON><PERSON>tialize,
  PlatformPrefix,
  ScenarioNameKey,
  SpecInfoKey,
  StepTitleKey,
  SuiteIDKey,
} from '@libs/constants/global.const';
import { LogStartEnd } from '@libs/decorators/log.decorator';
import { SpecInfo } from '@libs/gauge/spec-info';
import { SpecDataManager } from '@libs/gauge/spec.data-manager';
import { SuiteDataManager } from '@libs/gauge/suite.data.manager';
import Logger from '@libs/log/logger';
import {
  getAppiumDriver,
  terminateDriver,
} from '@libs/resources/appium.resource';
import {
  getSpecPage,
  terminateSpecPage,
} from '@libs/resources/playwright.resource';
import {
  getCurrentScenarioName,
  getCurrentSpecInfo,
} from '@libs/utils/gauge.util';
import { createUniqueID } from '@libs/utils/uuid.util';
import { writeFileSync } from 'fs';
import {
  AfterSpec,
  AfterStep,
  AfterSuite,
  BeforeScenario,
  BeforeSpec,
  BeforeStep,
  BeforeSuite,
  CustomScreenshotWriter,
  ExecutionContext,
} from 'gauge-ts';
import { Locator, Page, PageScreenshotOptions } from 'playwright';
import path = require('path');

export type ScreenshotOption = {
  target: Page | Locator;
  options: PageScreenshotOptions;
};

export type ScreenshotOptionModule = {
  screenshotOption: (
    page: Page,
    spec: SpecInfo,
    scenarioName: string,
  ) => Promise<Partial<ScreenshotOption>>;
};

export class ContextSteps {
  @LogStartEnd()
  @BeforeSuite()
  public async beforeSuite(ctx: ExecutionContext): Promise<void> {
    const suiteID = SuiteDataManager.put(
      SuiteIDKey,
      `${new Date().getTime()}_${createUniqueID()}`,
    );
    Logger.createNewLogDir(suiteID);
  }

  @BeforeScenario()
  public async beforeScenario(ctx: ExecutionContext) {
    SpecDataManager.put(ScenarioNameKey, ctx.getCurrentScenario().getName());
  }

  @LogStartEnd()
  @BeforeSpec()
  public async beforeSpec(ctx: ExecutionContext): Promise<void> {
    if (alreadyCalled()) {
      Logger.write(`commonBeforeSpec is already called. skip initialize.`);
      return;
    }
    Logger.write(`new spec start`);
    const si = SpecInfo.fromContext(ctx);
    Logger.write(`spec file: "${si.specFilePath}" -> spec id: "${si.specID}"`);
    SpecDataManager.put(SpecInfoKey, si);
    if (si.tags.includes(OmitInitialize)) {
      Logger.write(`${OmitInitialize} tag is found. skip initialize.`);
    }
    ContextSteps._logExecutionStatus(ctx, 'spec', 'start');
  }

  @LogStartEnd()
  @BeforeStep()
  public async beforeStep(ctx: ExecutionContext): Promise<void> {
    ContextSteps._logExecutionStatus(ctx, 'step', 'start');
    SpecDataManager.put(StepTitleKey, ctx.getCurrentStep()?.getText() ?? '');
  }

  @LogStartEnd()
  @AfterStep()
  public async afterStep(ctx: ExecutionContext): Promise<void> {
    ContextSteps._logExecutionStatus(ctx, 'step', 'end');
  }

  @LogStartEnd()
  @AfterSpec()
  public async afterSpec(ctx: ExecutionContext): Promise<void> {
    if (!ctx.getCurrentSpec()?.getIsFailing()) {
      // teardown ??
    }
    await terminateSpecPage();
    await terminateDriver();
    ContextSteps._logExecutionStatus(ctx, 'spec', 'end');
  }

  @LogStartEnd()
  @AfterSuite()
  public async afterSuite(ctx: ExecutionContext): Promise<void> {}

  private static _logExecutionStatus(
    ctx: ExecutionContext,
    stepOrSpec: string,
    startOrEnd: string,
  ): void {
    const si = getCurrentSpecInfo();
    const text = ctx.getCurrentStep()?.getText();
    Logger.write(
      `${stepOrSpec} ${startOrEnd}: ${si.specFilePath}:${text ?? ''}`,
      1,
    );
  }

  @CustomScreenshotWriter()
  public async screenshot() {
    const spec = getCurrentSpecInfo();
    if (!spec) return '';

    const specFile = path.basename(spec.specFilePath);
    const scenarioName = getCurrentScenarioName();
    if (!scenarioName) return '';

    const outputDir = process.env['gauge_screenshots_dir'];
    const safeScenarioName = scenarioName.replace(/\[]\/\$\*\.\^\/&/, '');
    const basePath = specFile + '-' + safeScenarioName;

    const number = getSpecImageNumber();
    const filePath = path.join(outputDir, `${basePath}-${number}.png`);
    const currentPlatform: string = await SpecDataManager.get(StepTitleKey);

    if (currentPlatform.startsWith(PlatformPrefix.WEB)) {
      const { target, options } = await getScreenshotOption(spec, scenarioName);

      Object.assign(options, { path: filePath });

      await target.screenshot(options);
    }

    if (currentPlatform.startsWith(PlatformPrefix.MOBILE)) {
      const driver = await getAppiumDriver();
      const screenshot = await driver.takeScreenshot();
      writeFileSync(filePath, screenshot, 'base64');
    }

    return path.basename(filePath);
  }
}

function getSpecImageNumber(): number {
  const key = 'screenShotNumber';
  if (!SpecDataManager.has(key)) {
    SpecDataManager.put(key, 1);
    return 1;
  }

  const number = SpecDataManager.get<number>(key) + 1;
  SpecDataManager.put(key, number);
  return number;
}

async function getScreenshotOption(
  spec: SpecInfo,
  scenarioName: string,
): Promise<ScreenshotOption> {
  const page = await getSpecPage();
  const defaultOption = { fullPage: false };

  const screenshotOptionFile = spec.findScreenshotOptionFile();
  if (!screenshotOptionFile) {
    return { target: page, options: defaultOption };
  }

  const mod = (await import(screenshotOptionFile)) as ScreenshotOptionModule;
  if (!mod.screenshotOption) {
    throw new Error(
      `screenshotOption function is not exported in ${screenshotOptionFile}`,
    );
  }
  const options = await mod.screenshotOption(page, spec, scenarioName);
  return {
    target: options.target ?? page,
    options: Object.assign(defaultOption, options.options ?? {}),
  };
}

function alreadyCalled(): boolean {
  try {
    return !!getCurrentSpecInfo();
  } catch (e) {
    return false;
  }
}
