# SANAN Kid - Web Admin - Sidebar - Finance

tags: web,sanankid-web-admin,FQAC

## FQAC_000019

### Login page
* Login sanankid web with "0854124589" and "27041998", then select profile "Automation Test" of school "Trường Trường <PERSON>"

### Input data
* Web: Click on menu "Tài chính" on sidebar
* Web: Click on item "Thiết lập mức thu" on menu
* Web: Click button with "Thêm mới"
* Web: Click with "Thêm mới đối tượng áp dụng" exact
* Fill fee name "TestFee", fee "1000000" and unit "112" to add new fee
* Web: Click on "Toàn Trường" to open scope menu
* Web: Click on item to choose scope "Lớp"
* Wait "3" second
* Web: Click on classMenu to open class menu
* Web: Click on item to choose class "Big"
* Web: Keyboard press key "Escape"
* Web: Click button with "Thêm mới"
* Web: Wait for page stable
* Web: On row that has "TestFee", doing step:
* Web: => Click on edit button (button 1) on column "Tác vụ"
* Web: Wait for page stable
* Web: Click on classMenu to open class menu
* Web: Click on item to choose class "Big"
* Web: Click on item to choose class "Mini"
* Web: Keyboard press key "Escape"
* Web: Click button with "Cập nhật"
* Web: Wait for page stable
### Verify data
* Web: On row that has "Mini", doing step:
* Web: => Verify on column "Đối tượng áp dụng" has "Mini"
* Web: On row that has "TestFee", doing step:
* Web: => Click on edit button (button 2) on column "Tác vụ"
* Web: Click button with "Đồng ý" exact
* Wait "2" second