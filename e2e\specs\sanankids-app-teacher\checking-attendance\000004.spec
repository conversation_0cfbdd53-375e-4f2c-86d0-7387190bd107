# SANAN Kid - App teacher

tags: mobile,sanankid-app-teacher,android

table: e2e\specs\sanankids-app-teacher\checking-attendance\test-data\data.csv

## 000004
### Checking attendace with capture on checking attendance
* Login sanankid app teacher with <username> and <password>
* Mobile: Click with <class>
* Mobile: Click button allow permission controler
* Mobile: Click button attendance on today screen
* Mobile: Click on capture checkin attendance on checking attendance screen
* Mobile: Click button capture on checking attendance screen
* Mobile: Verify checkbox attendance is checked on checking attendance screen
* Mobile: Capture screenshot
* Mobile: Click with "Xác Nhận"
* Mobile: Click with "CÓ"
### Undo attendance
* Mobile: Click checkbox attendance on checking attendance screen
* Mobile: Click with "Xác Nhận"
* Mobile: Click with "CÓ"
### Checking attendace with QR
* Mobile: Click with "Điểm danh bằng mã QR"
* Mobile: Verify exact "Thông tin điểm danh" is visible
* Mobile: Verify exact <name student> is visible
* Mobile: Verify exact <class> is visible
* Mobile: Verify exact <date of brith> is visible
* Mobile: Verify exact <gender> is visible
* Mobile: Verify exact "Thông tin sai" is visible
* Mobile: Verify exact "Điểm danh" is visible
* Mobile: Capture screenshot
### Verify Checking attendace with QR success
* Mobile: Click with "Điểm danh"
* Mobile: Close and relunch teacher app
* Mobile: Click button attendance on today screen
* Mobile: Verify exact "Điểm danh" is visible
* Mobile: Verify exact "Đã đến" is visible
* Mobile: Verify checkbox attendance is checked on checking attendance screen
### Undo attendance
* Mobile: Click checkbox attendance on checking attendance screen
* Mobile: Click with "Xác Nhận"
* Mobile: Click with "CÓ"
* Wait "2" second
### Verify on web admin
* Web: Open sanankid web admin login page
* Web: Fill username <username> and <password> to login
* Web: Click button login to sanankid web admin
* Web: Wait for page stable
* Web: Click with <schoolName>
* Web: Click with <profileName>
* Web: Click on menu "Điểm danh học sinh" on sidebar
* Web: Click on item "Điểm danh học sinh" on menu
* Web: Wait for page stable
* Web: On row that has <class>, doing step:
* Web: => Click on edit button (button external link) on column "colName"
* Web: Wait for page stable
* Web: On row that has <name student>, doing step:
* Web: => Verify on column "Tác vụ" has "check"
* Web: Wait for page stable
* Web: Capture screenshot