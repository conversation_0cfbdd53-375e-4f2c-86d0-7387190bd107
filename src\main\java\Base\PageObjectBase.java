package Base;

import Utilities.Log;
import io.appium.java_client.MobileDriver;
import io.appium.java_client.MobileElement;
import org.apache.commons.io.FileUtils;
import org.openqa.selenium.By;
import org.openqa.selenium.OutputType;
import org.openqa.selenium.TakesScreenshot;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;

import java.io.File;
import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

public class PageObjectBase {
    public final int MAX_WAIT_TIME = 30;
    public static MobileDriver<MobileElement> appiumDriver;

    public PageObjectBase(MobileDriver<MobileElement> appiumDriver) {
        PageObjectBase.appiumDriver = appiumDriver;
    }

    public void clickOnElement(By locator, String stepInfo) {
        MobileElement element = waitForElementPresence(locator, MAX_WAIT_TIME);
        element.click();
        Log.info("Click on element: " + "\"" + stepInfo + "\"");
    }

    public void formInputByLocator(By locator, String value) {
        MobileElement element = waitForElementPresence(locator, MAX_WAIT_TIME);
        element.clear();
        element.sendKeys(value);
        Log.info("Enter value: " + "\"" + value + "\"");
    }

    // click checkbox, ....
    public void clickOnElementSuccess(By locator, String elementName) {
        MobileElement element = waitForElementPresence(locator, MAX_WAIT_TIME);
        element.click();
        if (isElementClicked(element)) {
            Log.info("Click successful on element: " + "\"" + elementName + "\"");
        } else {
            Log.info("Click fail on element: " + "\"" + elementName + "\"");
        }
    }

    public static MobileElement waitForElementPresence(By locator, int timeoutInSeconds) {
        WebDriverWait wait = new WebDriverWait(appiumDriver, timeoutInSeconds);
        return (MobileElement) wait.until(ExpectedConditions.presenceOfElementLocated(locator));
    }

    public Boolean waitElementIsDisplay(By locator, int timeoutInSeconds) {
        WebDriverWait wait = new WebDriverWait(appiumDriver, timeoutInSeconds);
        MobileElement element = (MobileElement) wait.until(ExpectedConditions.presenceOfElementLocated(locator));
        return element.isDisplayed();
    }

    public Boolean isElementClicked(MobileElement element) {
        return element.isDisplayed() && element.isEnabled();
    }

    public void CaptureScreenShot(String screenName) {
        String destinationPath;
        DateFormat dateFormat;
        // Give folder name to store screenshots.
        destinationPath = "screenshot";
        // Capture screenshot.
        File scrFile = ((TakesScreenshot) appiumDriver).getScreenshotAs(OutputType.FILE);
        // Set date format to set It as screenshot file name.
        dateFormat = new SimpleDateFormat("dd-MMM-yyyy__hh0-mm-ss__aa");
        // Create folder under project with name "screenshot" provided to destDir.
        new File(destinationPath).mkdirs();
        // Set file name with current date time.
        String destinationFile = screenName + dateFormat.format(new Date()) + ".png";

        try {
            // Copy paste file at destination folder location
            FileUtils.copyFile(scrFile, new File(destinationPath + "/" + destinationFile));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
