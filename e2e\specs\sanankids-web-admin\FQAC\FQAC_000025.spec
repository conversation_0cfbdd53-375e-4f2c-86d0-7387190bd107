# SANAN Kid - Web Admin - Sidebar - Finance

tags: web,sanankid-web-admin,FQAC

## FQAC_0000025

### Login page
* Login sanankid web with "0854124589" and "27041998", then select profile "Automation Test" of school "Trường Tr<PERSON><PERSON><PERSON>"

### Input data
* Web: Click on menu "Tài chính" on sidebar
* Web: Click on item "Thiết lập mức thu" on menu

### Verify data
* Web: Click item "Trông muộn" on tab
* Web: Click button with "Chỉnh sửa"
* Web: Wait for page stable
* Web: Click icon create to create late pickup fee
* Web: Edit late pickup fee "20000"
* Web: Edit late pickup time start "5", end "10"
* Wait "2" second
* Web: Click button with "Cập nhật"
* Web: Click button with "Chỉnh sửa"
* Web: Edit late pickup fee "50000"
* Web: Click button with "Cập nhật"
* Web: Wait for page stable
* Web: On row that has "5", doing step:
* Web: => Verify on column " <PERSON><PERSON><PERSON> phí " has "50,000 VND"
### Clear Data
* Web: Click button with "Chỉnh sửa"
* Web: Click icon delete to delete late pickup fee
* Web: Click button with "Cập nhật"
* Wait "2" second