import { ScreenBase } from '@libs/bases/mobile/screen.base';

export class LoginScreen extends ScreenBase {
  constructor(public driver: WebdriverIO.Browser) {
    super(driver);
  }

  public usernameInput() {
    return this.getInputByPlaceholder('<PERSON><PERSON> điện thoại của bạn');
  }

  public passwordInput() {
    return this.getInputByPlaceholder('<PERSON><PERSON><PERSON><PERSON> mật khẩu');
  }

  public continueButton() {
    return this.getElementByText('<PERSON><PERSON>ế<PERSON> Tục');
  }

  public permissionControlerMedia() {
    return this.getElementByXpath('widget.Button[@resource-id="com.android.permissioncontroller:id/permission_allow_foreground_only_button"]');
  }

  public permissionControlerFiles() {
    return this.getElementByXpath('widget.Button[@resource-id="com.android.permissioncontroller:id/permission_allow_button"]');
  }
}
