import { Step } from 'gauge-ts';

export class CommonSteps {
  @Step('Wait <msec> milisecond')
  public async waitMilliSeconds(msecs: string): Promise<void> {
    await new Promise((resolve) => setTimeout(resolve, parseInt(msecs, 10)));
  }

  @Step('Wait <seconds> second')
  public async waitSeconds(seconds: string): Promise<void> {
    await new Promise((resolve) =>
      setTimeout(resolve, parseFloat(seconds) * 1000),
    );
  }
}
