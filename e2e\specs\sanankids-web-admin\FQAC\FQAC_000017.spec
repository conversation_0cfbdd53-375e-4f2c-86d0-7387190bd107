# SANAN Kid - Web Admin - Sidebar - Finance

tags: web,sanankid-web-admin,FQAC

## FQAC_0000017

### Login page
* Login sanankid web with "0854124589" and "27041998", then select profile "Automation Test" of school "Trường Trư<PERSON><PERSON>"

### Input data
* Web: Click on menu "Tài chính" on sidebar
* Web: Click on item "Thiết lập mức thu" on menu
* Web: Click button with "Thêm mới"
* Web: Click with "Thêm mới đối tượng áp dụng" exact
* Fill fee name "TestFee", fee "1000000" and unit "112" to add new fee
* Web: Click button with "Thêm mới"
* Web: Wait for page stable
* Web: On row that has "TestFee", doing step:
* Web: => Click on edit button (button 1) on column "Tác vụ"
* Web: Wait for page stable
* Web: Fill fee value "520520"
* Web: Click button with "<PERSON><PERSON><PERSON> nhật"
* Web: Wait for page stable
### Verify data
* Web: On row that has "Toàn trường", doing step:
* Web: => Verify on column "Mức phí" has "520,520 VND"
* Web: On row that has "TestFee", doing step:
* Web: => Click on edit button (button 2) on column "Tác vụ"
* Web: Click button with "Đồng ý" exact
* Wait "2" second