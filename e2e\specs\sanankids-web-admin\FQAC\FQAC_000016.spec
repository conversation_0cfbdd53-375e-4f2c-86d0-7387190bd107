# SANAN Kid - Web Admin - Sidebar - Finance

tags: web,sanankid-web-admin,FQAC

## FQAC_0000016

### Login page
* Login sanankid web with "0854124589" and "27041998", then select profile "Automation Test" of school "Trường Trư<PERSON>"

### Input data
* Web: Click on menu "Tài chính" on sidebar
* Web: Click on item "Thiết lập mức thu" on menu
* Web: Click button with "Thêm mới"
* Web: Click with "Thêm mới đối tượng áp dụng" exact
* Fill fee name "TestFee", fee "1000000" and unit "112" to add new fee
* Web: Click button with "Thêm mới"
* Web: On row that has "TestFee", doing step:
* Web: => Click on edit button (button 1) on column "Tác vụ"
* Wait "2" second
* Web: Input field by placeholder "Đồng phục" with "Change fee name"
* Web: Click button with "<PERSON><PERSON><PERSON> nhật"
### Verify data
* Web: On row that has "Change fee name", doing step:
* Web: => Verify on column "Tên khoản phí" has "Change fee name"
* Web: On row that has "Change fee name", doing step:
* Web: => Click on edit button (button 2) on column "Tác vụ"
* Web: Click button with "Đồng ý" exact
* Wait "2" second