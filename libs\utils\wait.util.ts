import { Locator, Page } from 'playwright';
import {
  AnimationDurationThreshold,
  WebStabilityCheckWaitDurationMS,
  WebStabilityCheckWaitRepeatCount,
} from '@libs/constants/config.const';
import { isLocator } from './locator.util';

export const waitForHard = async (locatorOrPage: Locator | Page) => {
  const page = isLocator(locatorOrPage) ? locatorOrPage.page() : locatorOrPage;
  await Promise.all([
    page.waitForLoadState(),
    page.waitForFunction(() => document.readyState === 'complete'),
  ]);
};

export const waitForText = async (
  locatorOrPage: Locator | Page,
  text: string,
) => {
  const { locator, page } = isLocator(locatorOrPage)
    ? { locator: locatorOrPage, page: locatorOrPage.page() }
    : { locator: null, page: locatorOrPage };
  for (let i = 0; i < WebStabilityCheckWaitRepeatCount; i++) {
    if (locator && (await locatorHasText(locator, text))) {
      return;
    }
    const elem = locator ? locator.getByText(text) : page.getByText(text);
    if ((await elem.count()) > 0) {
      return;
    }
    await page.waitForTimeout(WebStabilityCheckWaitDurationMS);
  }
  throw new Error(`${text} does not appear`);
};

export const waitForAnimation = async (
  locatorOrPage: Locator | Page,
  threshold: number = AnimationDurationThreshold,
) => {
  const page = isLocator(locatorOrPage) ? locatorOrPage.page() : locatorOrPage;
  await page.waitForFunction((threshold: number) => {
    const elements = document.querySelectorAll('*');
    return Array.from(elements).every((el) => {
      const { transitionDuration, animationDuration } =
        window.getComputedStyle(el);
      const hasTransition = parseFloat(transitionDuration) > threshold;
      const hasAnimation = parseFloat(animationDuration) > threshold;
      return !hasTransition && !hasAnimation;
    });
  }, threshold);
};

const locatorHasText = async (locator: Locator, text: string) => {
  return (
    (await locator.all()).findIndex(async (elem) => {
      return (await elem.textContent()).includes(text);
    }) >= 0
  );
};
