import { AppsTag } from '@libs/constants/tag.const';
import { AppiumDriver } from './appium.resource';

export abstract class MobileResource {
  constructor(public driver: AppiumDriver) {}

  get getPrefixXpath() {
    const platform = process.env.PLATFORM;
    switch (platform) {
      case AppsTag.PLATFORM_ANDROID:
        return '//android';
      case AppsTag.PLATFORM_IOS:
        return '';
      default:
        return '';
    }
  }

  get isAnroid() {
    return this.driver.isAndroid;
  }

  get isIos() {
    return this.driver.isIOS;
  }

  getInputByPlaceholder(placeholder: string) {
    return this.driver.$(
      `${this.getPrefixXpath}.widget.EditText[@text="${placeholder}"]`,
    );
  }

  getElementByText(text: string) {
    return this.driver.$(`${this.getPrefixXpath}.widget.EditText[@text="${text}"]`);
  }

  getElementByXpath(xpath: string) {
    return this.driver.$(`${this.getPrefixXpath}.${xpath}`);
  }
}
