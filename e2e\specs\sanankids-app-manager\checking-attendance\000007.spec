# SANAN Kid - App manager

tags: mobile,sanankid-app-manager,android

table: e2e\specs\sanankids-app-manager\checking-attendance\test-data\data.csv

## 000007
### Verify on web-admin
* Web: Open sanankid web admin login page
* Web: Fill username <username> and <password> to login
* Web: Click button login to sanankid web admin
* Web: Wait for page stable
* Web: Click with <schoolName>
* Web: Click with <profileName>
* Web: Click on menu "Điểm danh học sinh" on sidebar
* Web: Click on item "Điểm danh học sinh" on menu
* Web: Wait for page stable
* Web: On row that has <class>, doing step:
* Web: => Click on edit button (button external link) on column "colName"
* Web: Wait for page stable
* Web: On row that has <name student>, doing step:
* Web: => Verify on column "Tác vụ" has "check"
* Web: => Click on edit button (button checkin 1) on column "colName"
* Web: Wait for page stable
* Web: Capture screenshot
* Web: => Click on edit button (button undo 1) on column "colName"
### Checking attendace
* Login sanankid app manager with <username> and <password>
* Mobile: Click with <profileName>
* Mobile: Click button allow permission controler
* Mobile: Click with "Điểm danh"
* Mobile: Verify exact <name student> is visible
* Mobile: Verify exact <class> is visible
* Mobile: Verify exact <date of brith> is visible
* Mobile: Verify exact <gender> is visible
* Mobile: Verify exact "Vắng không phép" is visible
* Mobile: Click button checkin on checking attendance manager screen
* Mobile: Capture screenshot
* Mobile: Click button undo on checking attendance manager screen
* Mobile: Click with "CÓ"
* Mobile: Capture screenshot
