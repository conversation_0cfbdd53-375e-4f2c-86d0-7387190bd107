import { PageBase } from '@libs/bases/web/page.base';
import { LocatorGetter } from '@libs/decorators/locator.decorator';
import { Page } from 'playwright';

export class Header extends PageBase {
  public constructor(page: Page) {
    super(page);
  }

  @LocatorGetter()
  public async appHeader() {
    return this.page.locator('app-header');
  }

  @LocatorGetter()
  public async accountInfoLocator() {
    return (await this.appHeader()).locator('.username');
  }

  @LocatorGetter()
  public async accountDropDownMenu() {
    return this.page.locator('.mat-menu-panel');
  }

  @LocatorGetter()
  public async languageLocator() {
    return (await this.appHeader()).locator('span.mat-menu-trigger');
  }

  @LocatorGetter()
  public async languageDropDownMenu() {
    return this.page.locator('.mat-menu-content');
  }

  @LocatorGetter()
  public async chatButton() {
    return this.page.locator('.chat-wrapper > img');
  }

  @LocatorGetter()
  public async logoImage() {
    return this.page.locator('.logo_image');
  }

  @LocatorGetter()
  public async profileSwitchButton() {
    return this.page.locator('#mat-select-value-3 > span > span');
  }

  @LocatorGetter()
  public async profileSwitchDropDown() {
    return this.page.locator('#mat-select-2-panel');
  }

  @LocatorGetter()
  public async notiButton() {
    return (await this.appHeader()).locator('.notifications');
  }

  @LocatorGetter()
  public async notiDropDownMenu() {
    return this.page.locator('#mat-menu-panel-1');
  }
}
