package AppParentModules.Login.Locators;

import org.openqa.selenium.By;

public class LoginLocators {
    //Textbox [Phone number] [Screen - Login]
    public static By textboxPhoneNumber = By.xpath("//android.widget.EditText[@text=\"S<PERSON> điện thoại của bạn\"]");

    //Textbox [Password] [Screen - Login]
    public static By textboxPassword = By.xpath("//android.widget.EditText[@text=\"Nhập mật khẩu\"]");

    //Button [Tiep tuc [Screen - Login]
    public static By buttonContinues = By.xpath("//android.widget.TextView[@text=\"Tiếp Tục\"]");

    //Button [Login] [Screen - Login]
    public static By buttonLogin = By.xpath("//android.widget.TextView[@text=\"Đăng Nhập\"]");

    // List [Danh sach ho so] [After click button - Login]
    public static By profileList = By.xpath("//android.view.View[@text=\"<PERSON>h sách hồ sơ\"]");

    // Click on [Ho so 1]
    public static By profile1 = By.xpath("//android.widget.TextView[@text=\"Bé đức còi cọc\"]");
}
