import { ElementBase } from '@libs/bases/web/element.base';
import { LocatorGetter } from '@libs/decorators/locator.decorator';
import { Locator } from '@playwright/test';

export class ProfileSwitchingDropDownMenu extends ElementBase {
  public constructor(elem: Locator) {
    super(elem);
  }

  @LocatorGetter()
  public async menuItemLocator(caption: string) {
    return this.getElementByTextExact(caption);
  }
}
