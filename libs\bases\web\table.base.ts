import { Locator } from 'playwright';
import { ElementBase } from '@libs/bases/web/element.base';
import { LocatorGetter } from '@libs/decorators/locator.decorator';
import { DOMTableRow, TableUtil } from '@libs/utils/table.util';

export class TableBase extends ElementBase {
  public constructor(locator: Locator) {
    super(locator);
  }

  @LocatorGetter()
  public async cellByColumnName(name: string) {
    const col = await TableUtil.getColumnIndexByCaption(
      name,
      new DOMTableRow(this.locator),
    );

    return this.locator.getByRole('cell').nth(col);
  }
}
