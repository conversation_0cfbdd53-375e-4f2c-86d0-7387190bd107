# Convetion dự án:

- Tạo issue dựa trên trên file testcase

* Title: tên scenario (VD: FQAC_000001)
* Mô tả isuse: viết ngắn gọn phần cần làm và đính kèm file testcase liên quan.

- Đặt tên nhánh: develop\_{issue_id}.
- Tên commit: "#{issue_id} message".

# Quy tắc đặt tên

- Các thành phần dùng chung của web, app cho vào folder shared của từng project.
- Tên file đặt theo study case (VD: login.page.ts, login.step.ts).

* NOTES: Mỗi file spec phải có gắn tag platform, tên ứng dụng, ID test case(VD: FQAC)

- WEB: web, sanankid-web-admin,...
- MOBILE: mobile, sanankid-app-parent,...
