# SANAN Kid - Web Admin - Decentralization - Meal

tags: web,mobile,sanankid-web-admin,MQAA

## 000003_1

### Login page
* Login sanankid web with "0854124589" and "27041998", then select profile "Quản lý toàn trường" of school "Trường Trung Tâm Automation Testing"
### edit meal list 
* Web: Click on menu "Ăn uống" on sidebar
* Web: Click on item "Cài đặt bữa ăn" on menu
* Web: Verify exact "Thêm mới" is not visible
* Web: On row that has "breakfast", doing step:
* Web: => Verify on column "Tác vụ" does not have "edit"
* Web: => Verify on column "Tác vụ" does not have "delete"
* Web: On row that has "lunch", doing step:
* Web: => Verify on column "Tác vụ" does not have "edit"
* Web: => Verify on column "Tác vụ" does not have "delete"
* Web: On row that has "dinner", doing step:
* Web: => Verify on column "Tác vụ" does not have "edit"
* Web: => Verify on column "Tác vụ" does not have "delete"
* Web: Logout sanankid web admin

## 000003_2

### Login page
* Login sanankid web with "0936275212" and "27041998", then select profile "autotest_001" of school "Trường Trung Tâm Automation Testing"
### Edit meal list 
* Web: Click on menu "Ăn uống" on sidebar
* Web: Click on item "Cài đặt bữa ăn" on menu
* Web: Verify exact "Thêm mới" is visible
* Web: On row that has "breakfast", doing step:
* Web: => Verify on column "Tác vụ" has "edit"
* Web: => Verify on column "Tác vụ" has "delete"
* Web: On row that has "lunch", doing step:
* Web: => Verify on column "Tác vụ" has "edit"
* Web: => Verify on column "Tác vụ" has "delete"
* Web: On row that has "dinner", doing step:
* Web: => Verify on column "Tác vụ" has "edit"
* Web: => Verify on column "Tác vụ" has "delete"
* Web: Logout sanankid web admin
