import { waitForText } from '@libs/utils/wait.util';
import { Locator, Page } from 'playwright';

export abstract class WebResource {
  public page = this.pageOrLocator as Page;
  public locator = this.pageOrLocator as Locator;

  protected constructor(public pageOrLocator: Page | Locator) {}

  public async waitForText(text: string) {
    await waitForText(this.pageOrLocator, text);
  }

  public getElementByTextExact(text: string): Locator {
    return this.pageOrLocator.getByText(text, { exact: true });
  }

  public getElementByCssSelector(cssSelector: string): Locator {
    return this.pageOrLocator.locator(cssSelector);
  }

  public getElementByText(text: string): Locator {
    return this.pageOrLocator.getByText(text);
  }

  public getElementByPlaceHolder(text: string): Locator {
    return this.pageOrLocator.getByPlaceholder(text);
  }

  public getElementByLabel(label: string): Locator {
    return this.pageOrLocator.getByLabel(label);
  }

  public getButtonByName(name: string): Locator {
    return this.pageOrLocator.getByRole('button', { name });
  }

  public getByAltText(text: string): Locator {
    return this.pageOrLocator.getByAltText(text);
  }
}
