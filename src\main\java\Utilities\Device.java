package Utilities;

import com.google.gson.annotations.SerializedName;

public class Device {
    @SerializedName("device_name")
    public String deviceName;
    @SerializedName("device_status")
    public Boolean deviceStatus;
    @SerializedName("device_UDID")
    public String deviceUDID;
    @SerializedName("OS")
    public String deviceOS;

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public Boolean getDeviceStatus() {
        return deviceStatus;
    }

    public void setDeviceStatus(Boolean deviceStatus) {
        this.deviceStatus = deviceStatus;
    }

    public String getDeviceUDID() {
        return deviceUDID;
    }

    public void setDeviceUDID(String deviceUDID) {
        this.deviceUDID = deviceUDID;
    }

    public String getDeviceOS() {
        return deviceOS;
    }

    public void setDeviceOS(String deviceOS) {
        this.deviceOS = deviceOS;
    }
}
