import { ElementBase } from '@libs/bases/web/element.base';
import { LocatorGetter } from '@libs/decorators/locator.decorator';
import { Locator } from '@playwright/test';

export class LanguageDropDownMenu extends ElementBase {
  public constructor(elem: Locator) {
    super(elem);
  }

  @LocatorGetter()
  public async languageSelection(language: string) {
    const index = ['Vietnamese', 'Japanese', 'English'].indexOf(language);
    return this.getElementByCssSelector('span').nth(index);
  }
}
