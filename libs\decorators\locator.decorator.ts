import { waitForAnimation, waitForHard } from '@libs/utils/wait.util';
import { Locator } from 'playwright';

export type WaitType = 'waitFor' | 'waitForHard' | 'waitForAnimation';

export type WaitForLocatorOptions = {
  waitTypes: WaitType[];
};

const defaultOptions: WaitForLocatorOptions = {
  waitTypes: ['waitForHard'],
};

export function LocatorGetter(options: Partial<WaitForLocatorOptions> = null) {
  return (
    _target: object,
    _propertyKey: string,
    descriptor: PropertyDescriptor,
  ) => {
    const originalMethod = descriptor.value;
    const opts = { ...defaultOptions, ...(options ?? {}) };
    descriptor.value = async function (...args: any) {
      const result = await originalMethod.apply(this, args);
      const promises = preparePromises(result, opts.waitTypes);
      await Promise.all(promises);
      return result;
    };
  };
}

const preparePromises = (locator: Locator, waitTypes: WaitType[]) => {
  return waitTypes.map((waitType) => {
    switch (waitType) {
      case 'waitFor':
        return locator.waitFor();
      case 'waitForHard':
        return waitForHard(locator);
      case 'waitForAnimation':
        return waitForAnimation(locator);
    }
  });
};
