import {
  DefaultLogFileName,
  ExecutionLogDirectory,
  LogDirectory,
} from '@libs/constants/global.const';
import { getCurrentSpecInfo } from '@libs/utils/gauge.util';
import { Mutex } from 'await-semaphore';
import { appendFileSync, mkdirSync } from 'fs';
import { join, parse } from 'path';

import CallSite = NodeJS.CallSite;

type CallStack = {
  error: Error;
  callSites: NodeJS.CallSite[];
};

export default class Logger {
  private static _logDirPath: string | null = null;

  public static createNewLogDir(suiteID: string): void {
    this._logDirPath = join(LogDirectory, ExecutionLogDirectory, suiteID);
    mkdirSync(this._logDirPath, { recursive: true });
  }

  public static get logDirPath(): string {
    if (this._logDirPath == null) {
      throw new Error('Logger.createNewLogDir() is not called');
    }
    return this._logDirPath;
  }

  private static async _write(msg: string, stackIndex: number): Promise<void> {
    if (this._logDirPath == null) {
      console.error('Logger.createNewLogDir() is not called');
    }
    const path = this._getLogFilePath();
    this._prepareLogFile(path);
    const time = _createTimeString();
    const caller = this._callerFileName(stackIndex);
    const mutex = new Mutex();
    const release = await mutex.acquire();
    try {
      appendFileSync(
        path,
        `${time} [TERRIBLE-TEST-ENV] [INFO] [${caller}] ${msg}` + '\n',
      );
    } finally {
      release();
    }
  }

  private static _prepareLogFile(path: string): void {
    const elem = parse(path);
    mkdirSync(elem.dir, { recursive: true });
  }

  private static _getLogFilePath(): string {
    try {
      const si = getCurrentSpecInfo();
      return join(this._logDirPath, si.logFilePath);
    } catch (e) {
      return join(this._logDirPath, DefaultLogFileName);
    }
  }

  public static write(msg: string, stackIndex: number = 0): void {
    this._write(msg, stackIndex).then((_) => {});
  }

  private static _callerFileName(stackIndex: number): string {
    const prevPrepareStackTrace = Error.prepareStackTrace;
    Error.prepareStackTrace = (error, callSites) => {
      return { error, callSites } as CallStack;
    };
    const obj = { stack: null as unknown as CallStack };
    Error.captureStackTrace(obj);
    const sites = obj.stack.callSites.map((site) => site.toString()).reverse();
    Error.prepareStackTrace = prevPrepareStackTrace;
    const last = sites.findIndex((site) => site.includes('Logger'));
    sites.splice(last);
    const lastsite = this._lastSite(obj.stack.callSites, stackIndex);
    return this._removeBracket(lastsite);
  }

  private static _lastSite(sites: CallSite[], stackIndex: number): string {
    const names = sites.reverse().map((site) => site.toString());
    const last = names.findIndex((site) => site.includes('Logger'));
    names.splice(last);
    return names[names.length - 1 - stackIndex];
  }

  private static _removeBracket(lastsite: string): string {
    const path = /.+\(.+\)/.test(lastsite)
      ? lastsite.split('(')[1].split(')')[0]
      : lastsite;
    const paths = parse(path);
    return paths.base;
  }
}

function _createTimeString(date: Date = new Date()): string {
  const day = _zeroPadding(date.getDate(), 2);
  const month = _zeroPadding(date.getMonth() + 1, 2);
  const year = _zeroPadding(date.getFullYear(), 4);
  const hour = _zeroPadding(date.getHours(), 2);
  const min = _zeroPadding(date.getMinutes(), 2);
  const sec = _zeroPadding(date.getSeconds(), 2);
  const ms = _zeroPadding(date.getMilliseconds(), 3);
  return `${day}-${month}-${year} ${hour}:${min}:${sec}.${ms}`;
}

function _zeroPadding(num: number, digits: number): string {
  return num.toString().padStart(digits, '0');
}
