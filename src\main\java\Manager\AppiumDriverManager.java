package Manager;

import Utilities.ReadJson;
import io.appium.java_client.MobileDriver;
import io.appium.java_client.MobileElement;
import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.ios.IOSDriver;
import io.appium.java_client.ios.IOSElement;
import io.appium.java_client.remote.AutomationName;
import io.appium.java_client.remote.IOSMobileCapabilityType;
import io.appium.java_client.remote.MobileCapabilityType;
import org.openqa.selenium.Platform;
import org.openqa.selenium.remote.DesiredCapabilities;

import java.net.URL;
import java.util.concurrent.TimeUnit;

public class AppiumDriverManager {
    public  static MobileDriver<MobileElement> getAndroidDriver() {
        MobileDriver<MobileElement> androidDriver = null;
        try {
            DesiredCapabilities desiredCapabilities = new DesiredCapabilities();
            desiredCapabilities.setCapability(MobileCapabilityType.PLATFORM_NAME, Platform.ANDROID);
            desiredCapabilities.setCapability(MobileCapabilityType.AUTOMATION_NAME, AutomationName.ANDROID_UIAUTOMATOR2);
            desiredCapabilities.setCapability(MobileCapabilityType.UDID, ReadJson.getDevice().getDeviceUDID());
            desiredCapabilities.setCapability("appPackage", "com.sanankids.parent.dev");
            desiredCapabilities.setCapability("appActivity", "com.sanankids.parent.MainActivity");
            desiredCapabilities.setCapability("autoGrantPermissions", true);
            desiredCapabilities.setCapability("autoAcceptAlerts", true);

            URL url = new URL("http:localhost:4723/wd/hub");
            androidDriver = new AndroidDriver<>(url, desiredCapabilities);
            androidDriver.manage().timeouts().implicitlyWait(30L, TimeUnit.SECONDS);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return androidDriver;
    }

    public  static MobileDriver<MobileElement> getIOSDriver() {
        MobileDriver<MobileElement> iOSDriver = null;
        try {
            DesiredCapabilities desiredCapabilities = new DesiredCapabilities();
            desiredCapabilities.setCapability(MobileCapabilityType.PLATFORM_NAME, Platform.IOS);
            desiredCapabilities.setCapability(MobileCapabilityType.AUTOMATION_NAME, AutomationName.IOS_XCUI_TEST);
            desiredCapabilities.setCapability(MobileCapabilityType.DEVICE_NAME, ReadJson.getDevice().getDeviceName());
            desiredCapabilities.setCapability(MobileCapabilityType.PLATFORM_VERSION, "15.4.1");
            desiredCapabilities.setCapability(IOSMobileCapabilityType.LAUNCH_TIMEOUT, 500000);
            desiredCapabilities.setCapability("commandTimeouts", "12000");
            desiredCapabilities.setCapability(MobileCapabilityType.UDID, ReadJson.getDevice().getDeviceUDID());
            desiredCapabilities.setCapability("appPackage", "com.sanankids.parent.dev");
            desiredCapabilities.setCapability("appActivity", "com.sanankids.parent.MainActivity");
            desiredCapabilities.setCapability("autoGrantPermissions", true);
            desiredCapabilities.setCapability("autoAcceptAlerts", true);

            URL url = new URL("http:localhost:4723/wd/hub");
            iOSDriver = new IOSDriver<>(url, desiredCapabilities);
            iOSDriver.manage().timeouts().implicitlyWait(30L, TimeUnit.SECONDS);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return iOSDriver;
    }
}
