export const OmitInitialize = 'omit-initialize';

export const SuiteIDKey = 'suite_id_';
export const SpecInfoKey = 'spec_info_';
export const StepTitleKey = 'step_title_';
export const ScenarioNameKey = 'scenario_name_';

export const SpecDirectory = 'specs';
export const LogDirectory = 'logs';
export const ExecutionLogDirectory = 'execution';

export const ScreenshotOptionFileName = 'screenshot-option.ts';

export const SpecFileExtension = '.spec';
export const LogFileExtension = '.log';

export const DefaultLogFileName = `default${LogFileExtension}`;
export enum Domain {
  SanankidWebAdmin = 'Sanan Kids Web Admin',
}

export enum BrowserType {
  CHROMIUM = 'chromium',
  FIREFOX = 'firefox',
  WEBKIT = 'webkit',
}
export const BrowserRunTest =
  process.env.BROWSER_RUN_TEST || BrowserType.CHROMIUM;

export enum PlatformPrefix {
  WEB = 'Web:',
  MOBILE = 'Mobile:',
}
