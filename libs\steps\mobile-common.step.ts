import { AppsTag } from '@libs/constants/tag.const';
import { getAppiumDriver } from '@libs/resources/appium.resource';
import { expect } from '@libs/utils/expect.util';
import { Gauge, Step } from 'gauge-ts';

export class MobileCommonSteps {
  isAndroid: boolean = process.env.PLATFORM === AppsTag.PLATFORM_ANDROID;

  @Step('Mobile: Verify exact <text> is visible')
  public async assertTextToBeVisible(text: string) {
    const driver = await getAppiumDriver();
    const locator = await driver.$(
      `//*[contains(@text, '${text}')]`,
    );
    await locator.waitForDisplayed();
    await expect(locator).toBeVisible();
  }

  @Step('Mobile: Click with <text>')
  public async clickText(text: string) {
    const driver = await getAppiumDriver();
    const locator = await driver.$(
      `//*[contains(@text, '${text}')]`,
    );
    await locator.waitForDisplayed();
    await locator.click();
  }

  @Step('Mobile: Click button with <name>')
  public async clickButton(name: string) {
    const driver = await getAppiumDriver();
    const locator = await driver.$(`${name}`);
    await locator.waitForDisplayed();
    await locator.click();
  }

  @Step('Mobile: Capture screenshot')
  public async captureScreen() {
    await Gauge.captureScreenshot();
  }
}
