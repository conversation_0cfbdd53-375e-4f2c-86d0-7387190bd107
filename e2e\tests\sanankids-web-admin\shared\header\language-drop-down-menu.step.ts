import { LogStartEnd } from '@libs/decorators/log.decorator';
import { Step } from 'gauge-ts';
import { Header } from './header.component';
import { LanguageDropDownMenu } from './language-drop-down-menu.component';

export class LanguageDropDownMenuSteps {
  @LogStartEnd()
  @Step('Web: On language drop-down menu in the header:')
  public async pinAccountDropDownMenu() {
    const elem = await (await Header.getInstance()).languageDropDownMenu();
    LanguageDropDownMenu.setLocator(elem);
  }

  @LogStartEnd()
  @Step('Web: => Click <language> icon on the language drop-down menu')
  public async clickVietnamese(language: string) {
    const elem = await (
      await LanguageDropDownMenu.getInstance()
    ).languageSelection(language);
    await elem.click();
  }
}
