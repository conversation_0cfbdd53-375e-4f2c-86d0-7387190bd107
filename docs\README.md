# Setup dự án

- Cài node version ^16.14.0
- Cài gauge cli: npm i -g @getgauge/cli
- Cài plugin ts cho gauge: gauge install ts --version 0.1.1
- npm install

## Setup automation web

- npx playwright install chromium
- npx playwright install-deps

## Setup automation mobile

- npm install -g appium@latest
- npm install -g appium-doctor

* Android platform:

- Tải appium inspector GUI: https://github.com/appium/appium-inspector/releases/download/v2024.8.2/Appium-Inspector-2024.8.2-win-x64.exe
- Ki<PERSON><PERSON> tra các cài đặt nền trên nền tảng android: appium-doctor --android
- appium driver install uiautomator2
- Tải JDK 17: https://download.oracle.com/java/17/latest/jdk-17_windows-x64_bin.exe
- Setup biến môi trường JAVA_HOME=\path\to\bin, update path system %JAVA_HOME%\bin
- Kiểm tra cài đặt: java --version, javac --version
- Tải Android command-line: https://developer.android.com/studio?hl=vi#command-tools
- Setup biến môi trường ANDROID_HOME=\path\to\android
- sdkmanager --list
- sdkmanager "platform-tools" "build-tools;33.0.0"
- Update path system %ANDROID_HOME%\bin, %ANDROID_HOME%\platform-tools, %ANDROID_HOME%\build-tools
- Lấy device version: adb shell getprop ro.build.version.release
- Lấy device id: adb devices

* IOS platform:

- Tải appium inspector GUI: https://github.com/appium/appium-inspector/releases/download/v2024.8.2/Appium-Inspector-2024.8.2-mac-x64.dmg
- appium driver install xcuitest
- Install Xcode
- xcode-select --install
- sudo xcode-select -s /Applications/Xcode.app/Contents/Developer
- brew install carthage
- brew install libimobiledevice
- brew install ideviceinstaller

## Run spec

- WEB: npm run test web \path\to\spec
- MOBILE:
  - Check device id rồi thay đổi capacities ở nền tảng tương ứng
  - Start server appium: appium || appium --port=?
  - npm run test:android \path\to\spec
  - npm run test:ios \path\to\spec

### Cấu trúc dự án

├── e2e
│ |── specs
│ |──── concepts: Viết common steps
│ |──── [...project]: Tên dự án chứa các kịch bản test
│ |──── [...spec]: Mỗi folder spec gồm 1 file .spec viết step và 1 folder dataset chứa các file data.csv
│ |── tests
│ |── [...project]: Tên các dự án chứa phần triển khai kịch bản
│ |──── [...pages]: Mỗi page gồm 1 file locator, 1 file step implement
│ |──── shared: Các component spec implement của ứng dụng
├── libs
│ |── steps
│ |──── common: common step dùng cho tất cả
│ |──── web: common step dùng cho web
