{"name": "gauge-ts-template", "description": "Starter template for writing TypeScript tests for Gauge", "scripts": {"test:web": "gauge run", "test:ios": "gauge run --env \"default, ios\"", "test:android": "gauge run --env \"default, android\""}, "dependencies": {"await-semaphore": "^0.1.3", "gauge-ts": "0.1.1", "playwright": "^1.46.1", "ts-node": "^10.9.2", "uuid": "^10.0.0"}, "devDependencies": {"@playwright/test": "^1.46.1", "@types/uuid": "^10.0.0", "@wdio/appium-service": "^8.40.5", "tsconfig-paths": "^4.2.0", "typescript": "^4.9.5", "webdriverio": "^8.40.5"}}