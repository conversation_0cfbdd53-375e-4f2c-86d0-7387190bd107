package Listeners;

import Report.ExtentReportLogger;
import Report.ExtentReportManager;
import org.testng.*;

public class ExtentReportListeners implements ITestListener, ISuiteListener {
    @Override
    public void onStart(ISuite iSuite) {
        ExtentReportManager.initExtentReport();
    }

    @Override
    public void onFinish(ISuite iSuite) {
        ExtentReportManager.flushExtentReport();
    }

    @Override
    public void onTestStart(ITestResult iTestResult) {
        ExtentReportManager.createTest(iTestResult.getMethod().getMethodName());
        ExtentReportLogger.logInfo("Test - <b>" + iTestResult.getMethod().getMethodName() + "</b> is started");
    }

    @Override
    public void onTestSuccess(ITestResult iTestResult) {
        ExtentReportLogger.logPass("Test - <b>" + iTestResult.getMethod().getMethodName() + "</b> is passed");
    }

    @Override
    public void onTestFailure(ITestResult iTestResult) {
        ExtentReportLogger.logFail("Test - <b>" + iTestResult.getMethod().getMethodName() + "</b> is failed", iTestResult.getThrowable());
    }

    @Override
    public void onTestSkipped(ITestResult iTestResult) {
        ExtentReportLogger.logSkip("Test - <b>" + iTestResult.getMethod().getMethodName() + "</b> is skipped");
    }

    @Override
    public void onTestFailedButWithinSuccessPercentage(ITestResult iTestResult) {

    }

    @Override
    public void onStart(ITestContext iTestContext) {

    }

    @Override
    public void onFinish(ITestContext iTestContext) {

    }
}
