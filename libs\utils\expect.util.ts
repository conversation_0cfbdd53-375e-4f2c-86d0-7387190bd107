import { expect as assert } from '@playwright/test';

class Expect {
  constructor(public readonly element: WebdriverIO.Element) {}

  async toHaveText(expected: string | RegExp | ReadonlyArray<string | RegExp>) {
    return assert(await this.element.getText()).toEqual(expected);
  }

  async toBeVisible() {
    return assert(await this.element.isDisplayed()).toBeTruthy();
  }
}

export const expect = (element: WebdriverIO.Element) => {
  return new Expect(element);
};
