import {
  getPinnedLocator,
  setPinnedLocator,
} from '@libs/resources/playwright.resource';
import { WebResource } from '@libs/resources/web.resource';
import { Locator } from 'playwright';

export class ElementBase extends WebResource {
  public static setLocator(locator: Locator) {
    setPinnedLocator(locator);
  }

  public static getInstance<T extends WebResource>(
    this: new (locator: Locator) => T,
  ): T {
    return new this(getPinnedLocator());
  }
}
