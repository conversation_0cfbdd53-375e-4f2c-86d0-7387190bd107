package Report;

import com.aventstack.extentreports.MediaEntityBuilder;
import com.aventstack.extentreports.Status;
import com.aventstack.extentreports.markuputils.ExtentColor;
import com.aventstack.extentreports.markuputils.MarkupHelper;

public final class ExtentReportLogger {
    public static void logPass(String message) {
        ExtentReportManager.getExtentTest().pass(MarkupHelper.createLabel(message, ExtentColor.GREEN));
    }

    public static void logFail(String message, Throwable t) {
        ExtentReportManager.getExtentTest().fail(MarkupHelper.createLabel(message, ExtentColor.RED));
    }

    public static void logSkip(String message) {
        ExtentReportManager.getExtentTest().log(Status.SKIP, message);
    }

    public static void logInfo(String message) {
        ExtentReportManager.getExtentTest().log(Status.INFO, message);
    }
    //un implement
    public static void warning(String message) {
        ExtentReportManager.getExtentTest().log(Status.WARNING, message);
    }
}
