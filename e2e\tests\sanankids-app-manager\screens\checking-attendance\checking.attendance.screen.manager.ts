import { ScreenBase } from '@libs/bases/mobile/screen.base';

export class CheckingAttendanceManagerScreen extends ScreenBase {
  constructor(public driver: WebdriverIO.Browser) {
    super(driver);
  }

  public buttonCheckIn() {
    return this.driver.$('//android.widget.ScrollView/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup[3]/android.view.ViewGroup[2]/android.view.ViewGroup[2]/android.view.ViewGroup[1]');
  }

  public buttonCheckOut() {
    return this.driver.$('//android.widget.ScrollView/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup[3]/android.view.ViewGroup[2]/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup[1]');
  }

  public buttonUndoAll() {
    return this.driver.$('//android.widget.ScrollView/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup[3]/android.view.ViewGroup[2]');
  }

  public buttonUndo() {
    return this.driver.$('//android.widget.ScrollView/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup[3]/android.view.ViewGroup[2]/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup[2]');
  }

  public buttonLeave() {
    return this.driver.$('//android.widget.ScrollView/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup[3]/android.view.ViewGroup[2]/android.view.ViewGroup[2]/android.view.ViewGroup[2]');
  }
}