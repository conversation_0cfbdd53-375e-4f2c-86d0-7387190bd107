import { LogStartEnd } from '@libs/decorators/log.decorator';
import { expect } from '@playwright/test';
import { Step } from 'gauge-ts';
import { AccountDropDownMenu } from './account-drop-down-menu.component';
import { Header } from './header.component';

export class AccountDropDownMenuSteps {
  @LogStartEnd()
  @Step('Web: On account drop-down menu in the header:')
  public async pinAccountDropDownMenu() {
    const elem = await (await Header.getInstance()).accountDropDownMenu();
    AccountDropDownMenu.setLocator(elem);
  }

  @LogStartEnd()
  @Step([
    'Web: Now, click on <caption> of the account drop down menu',
    'Web: Then, click on <caption> of the account drop down menu',
    'Web: => Click on the <caption> of the account drop down menu',
  ])
  public async clickDropDownMenu(caption: string) {
    const elem = await (
      await AccountDropDownMenu.getInstance()
    ).menuItemLocator(caption);
    await elem.click();
  }

  @LogStartEnd()
  @Step(
    'Web: => Check whether <caption> is displayed on the account drop down menu',
  )
  public async verifyTextOnAccountDropDownMenu(caption: string) {
    const elem = await (
      await AccountDropDownMenu.getInstance()
    ).menuItemLocator(caption);
    await expect(elem).toContainText(caption);
  }
}
