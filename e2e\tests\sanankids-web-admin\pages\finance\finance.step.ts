import { TableBase } from '@libs/bases/web/table.base';
import { LogStartEnd } from '@libs/decorators/log.decorator';
import { SpecDataManager } from '@libs/gauge/spec.data-manager';
import { WebCommonSteps } from '@libs/steps/web-common.step';
import { Step } from 'gauge-ts';
import { expect } from 'playwright/test';
import { Finance } from './finance.page';
import { removeCommas } from '@libs/utils/remove-comma.util';
import { sum } from '@libs/utils/sum.utils';

export class FinanceSteps {
  @LogStartEnd()
  @Step(['Web: Verify exact <validationError> is visible on set new fee name'])
  public async nameIsRequiredInfield(errorName: string) {
    const elem = await (
      await Finance.getInstance()
    ).errorNameIsVisible(errorName);
    await expect(elem).toBeVisible();
  }

  @LogStartEnd()
  @Step(['Web: Verify exact <validationError> is visible on set new fee value'])
  public async valueIsRequiredInfield(errorName: string) {
    const elem = await (
      await Finance.getInstance()
    ).errorValueIsVisible(errorName);
    await expect(elem).toBeVisible();
  }

  @LogStartEnd()
  @Step(['Web: Verify exact <validationError> is visible on set new fee unit'])
  public async unitIsRequiredInfield(errorName: string) {
    const elem = await (
      await Finance.getInstance()
    ).errorUnitIsVisible(errorName);
    await expect(elem).toBeVisible();
  }

  @Step('Web: Fill fee value <fee>')
  public async fillFeeValue(fee: string) {
    const feeInput = await (await Finance.getInstance()).feeInput();
    await feeInput.clear();
    await feeInput.fill(fee);
  }

  @Step('Web: Fill fee unit <unit>')
  public async fillFeeUnit(unit: string) {
    const unitInput = await (await Finance.getInstance()).unitInput();
    await unitInput.clear();
    await unitInput.fill(unit);
  }

  @LogStartEnd()
  @Step(['Web: Verify exact <newFeeName> is on the student fee table'])
  public async newFeeNameIsVisible(newFeeName: string) {
    const elem = await (
      await Finance.getInstance()
    ).newFeeNameIsVisible(newFeeName);
    await expect(elem).toBeVisible();
  }

  @Step('Web: On row that has <name>, doing step:')
  public async pinTableByName(name: string) {
    let trimName = name.trim();
    const row = await WebCommonSteps.getRowByCaption(trimName);
    await row.scrollIntoViewIfNeeded();
    TableBase.setLocator(row);
  }
  //23
  @Step('Web: Fill value <value>')
  public async fillValue(value: string) {
    const elem = await (await Finance.getInstance()).locateFeeMeal();
    await elem.click();
    await elem.clear();
    await elem.fill(value);
  }
  //23
  @Step('Web: => Verify on column <colName> has <value>')
  public async verifyColumnText(colName: string, text: string) {
    const col = await TableBase.getInstance().cellByColumnName(colName);
    await col.scrollIntoViewIfNeeded();
    await expect(col).toContainText(text);
  }

  @Step('Web: => Verify on column <colName> has value <value>')
  public async verifyColumnNumber(colName: string, value: string) {
    const col = await TableBase.getInstance().cellByColumnName(colName);
    const valueOnCol = await removeCommas(col);
    expect(valueOnCol).toBe(value);
  }

  @Step([
    'Web: => Click on edit button (button 1) on column <colName>',
    'Web: => Click on edit button (button external link) on column <colName>',
    'Web: => Click on edit button (button undo 1) on column <colName>',
    'Web: => Click on edit button (button checkout 1) on column <colName>',
    'Web: => Click on edit button (button checkin 1) on column <colName>',
  ])
  public async clickBtnBeforeOnColName(colName: string) {
    const col = await TableBase.getInstance().cellByColumnName(colName);
    await col.getByRole('button').nth(0).scrollIntoViewIfNeeded();
    await col.getByRole('button').nth(0).click();
  }

  @Step([
    'Web: => Click on edit button (button 2) on column <colName>',
    'Web: => Click on edit button (button undo 2) on column <colName>',
    'Web: => Click on edit button (button checkout 2) on column <colName>',
    'Web: => Click on edit button (button checkin 2) on column <colName>',
  ]
  )
  public async clickBtnAfterOnColName(colName: string) {
    const col = await TableBase.getInstance().cellByColumnName(colName);
    await col.getByRole('button').nth(1).click();
  }

  @LogStartEnd()
  @Step('Web: Click on item to choose scope <scope>')
  public async chooseScope(scope: string) {
    const elemScope = await (await Finance.getInstance()).setScope(scope);
    await elemScope.click();
  }

  @Step('Web: Click on <scopeMenu> to open scope menu')
  public async chooseScopeMenu(scope: string) {
    const elemScope = await (await Finance.getInstance()).setScopeMenu(scope);
    await elemScope.click();
  }

  @Step('Web: Click on gradeMenu to open grade menu')
  public async openGradeMenu() {
    const gradeMenu = await (await Finance.getInstance()).gradeDropdownMenu();
    await gradeMenu.click();
  }

  @Step('Web: Click on item to choose grade <grade>')
  public async chooseGrade(grade: string) {
    const elemScope = await (
      await Finance.getInstance()
    ).setGrade(grade.trim());
    await elemScope.click();
  }

  @Step('Web: Click on classMenu to open class menu')
  public async openClassMenu() {
    const gradeMenu = await (await Finance.getInstance()).classDropdownMenu();
    await gradeMenu.click();
  }

  @Step('Web: Click on item to choose class <class>')
  public async chooseClass(className: string) {
    const elemScope = await (
      await Finance.getInstance()
    ).setClass(className.trim());
    await elemScope.click();
  }

  @Step('Web: Click the search bar to find student')
  public async clickStudentSearchBar() {
    const elem = await (await Finance.getInstance()).searchStudentBar();
    await elem.click();
  }

  @Step('Web: Click on second scope to choose scope')
  public async openSecondScopeMenu() {
    const gradeMenu = await (await Finance.getInstance()).locateSecondScope();
    await gradeMenu.click();
  }

  @Step('Web: Click on item to choose second scope <scope>')
  public async chooseSecondScope(scope: string) {
    const elemScope = await (await Finance.getInstance()).setSecondScope(scope);
    await elemScope.click();
  }

  @Step('Web: Expect submit-btn to be disabled')
  public async disableButton() {
    const btn = await (await Finance.getInstance()).disableButton();
    await expect(btn).toBeDisabled();
  }

  @Step('Web: Click the search bar')
  public async clickSearchBar() {
    const elem = await (await Finance.getInstance()).searchBar();
    await elem.click();
  }
  //22
  @Step('Web: Fill fee name <name> on searchbar')
  public async findFeebySearchBar(name: string) {
    const elem = await (await Finance.getInstance()).searchBar();
    await elem.fill(name);
  }
  //22
  @Step('Web: Click item <tabItem> on tab')
  public async chooseTab(tabItem: string) {
    const elem = await (await Finance.getInstance()).tab(tabItem);
    await elem.click();
  }

  @Step('Web: Click icon remove to remove fee')
  public async removeFee() {
    const elem = await (await Finance.getInstance()).removeButton();
    await elem.click();
  }

  @Step('Web: Click icon delete to delete late pickup fee')
  public async deleteFee() {
    const elem = await (await Finance.getInstance()).deleteButton();
    await elem.click();
  }
  //25,26,29
  @Step('Web: Click icon create to create late pickup fee')
  public async createFee() {
    const elem = await (await Finance.getInstance()).createButton();
    await elem.click();
  }
  //25,26,29
  @Step('Web: Edit late pickup fee <fee>')
  public async editFee(fee: string) {
    const elem = await (await Finance.getInstance()).locateLatePickUpFee();
    await elem.click();
    await elem.clear();
    await elem.fill(fee);
  }

  @Step('Web: Edit late pickup time start <start>, end <end>')
  public async editTimePickup(start: string, end: string) {
    const elemStart = await (
      await Finance.getInstance()
    ).locateLatePickUpStart();
    const elemEnd = await (await Finance.getInstance()).locateLatePickUpEnd();
    const values = [start, end];
    [elemStart, elemEnd].forEach(async (element, index) => {
      await element.click();
      await element.clear();
      await element.fill(values[index]);
    });
  }

  @LogStartEnd()
  @Step('Web: Verify exact <tab> is visible on new collection period')
  public async tabIsVisible(tab: string) {
    const elem = await (await Finance.getInstance()).tabIsVisible(tab);
    await expect(elem).toBeVisible();
  }

  @Step('Web: => Fill value <value> on column <colName>')
  public async fillFeeValueCollectionPeriod(value: string, colName: string) {
    const col = await TableBase.getInstance().cellByColumnName(colName);
    await col.dblclick();
    await col.locator('input').fill(value);
  }

  @Step('Web: On row that has <name>, click the checkbox in the first cell')
  public async clickCheckBox(name: string) {
    let trimName = name.trim();
    const row = await WebCommonSteps.getRowByCaption(trimName);
    const elem = row.locator('mat-cell').first().locator('mat-checkbox');
    await elem.click();
  }

  @Step('Web: Click icon to save sheet table')
  public async saveSheetTable() {
    const elem = await (await Finance.getInstance()).saveSheetTable();
    await elem.click();
  }

  @Step('Web: Click icon to confirm payment')
  public async confirmPaymentButton() {
    const elem = await (await Finance.getInstance()).confirmPayment();
    await elem.click();
  }

  @Step('Web: Open tab <name> in on new collection period')
  public async openTab(name: string) {
    const elem = await (await Finance.getInstance()).openTab(name);
    await elem.click();
  }

  @Step('Web: Verify the total amount on column is <value>')
  public async totalAmountCol(value: string) {
    const elem = await (await Finance.getInstance()).totalAmountCol(value);
    await expect(elem).toBeVisible();
  }

  //product
  @Step('Web: Get product of invoice value on column <col> * total invoice')
  public async totalAmountCard(col: string) {
    const totalInvoice = await (await Finance.getInstance()).totalInvoice();
    const totalInvoiceInt = await removeCommas(totalInvoice);
    const cell = (await TableBase.getInstance().cellByColumnName(col)).nth(0);
    const cellValue = await removeCommas(cell);
    const calculatedTotalAmountCard = totalInvoiceInt * cellValue;
    SpecDataManager.put('productOfTotal', calculatedTotalAmountCard);
  }

  @Step(
    'Web: Expect the statistic expected to be the product of invoice value on * total invoice',
  )
  public async stacticsExpected() {
    const stacticsExpected = await (
      await Finance.getInstance()
    ).statisticExpected();
    const stacticsExpectedOnCardInt = await removeCommas(stacticsExpected);
    const caculatedExpected = SpecDataManager.get('productOfTotal');
    expect(parseInt(stacticsExpectedOnCardInt)).toBe(caculatedExpected);
  }

  @Step(
    'Web: Get product of invoice value on column <col> * total unpaid invoice',
  )
  public async totalAmountUnpaidCard(col: string) {
    const totalUnpaidInvoice = await (
      await Finance.getInstance()
    ).totalUnpaidInvoice();
    const totalUnpaidInvoiceInt = await removeCommas(totalUnpaidInvoice);
    const cell = (await TableBase.getInstance().cellByColumnName(col)).nth(0);
    const cellValue = await removeCommas(cell);
    const calculatedTotalUnpaidAmountCard = totalUnpaidInvoiceInt * cellValue;
    SpecDataManager.put('productOfUnpaid', calculatedTotalUnpaidAmountCard);
  }

  @Step(
    'Web: Expect the statistic unpaid to be the product of invoice value on * unpaid invoice',
  )
  public async statisticUnpaid() {
    const statisticUnpaid = await (
      await Finance.getInstance()
    ).statisticUnpaid();
    const stacticsUnpaidOnCardInt = await removeCommas(statisticUnpaid);
    const caculatedUnpaid = SpecDataManager.get('productOfUnpaid');
    expect(parseInt(stacticsUnpaidOnCardInt)).toBe(caculatedUnpaid);
  }

  @Step(
    'Web: Get product of invoice value on column <col> * total paid invoice',
  )
  public async totalAmountPaidCard(col: string) {
    const totalPaidInvoice = await (
      await Finance.getInstance()
    ).totalPaidInvoice();
    const totalPaidInvoiceInt = await removeCommas(totalPaidInvoice);
    const cell = (await TableBase.getInstance().cellByColumnName(col)).nth(0);
    const cellValue = await removeCommas(cell);
    const calculatedTotalPaidAmountCard = totalPaidInvoiceInt * cellValue;
    SpecDataManager.put('productOfPaid', calculatedTotalPaidAmountCard);
  }

  @Step(
    'Web: Expect the statistic paid to be the product of invoice value on * paid invoice',
  )
  public async statisticPaid() {
    const stacticsPaid = await (await Finance.getInstance()).statisticPaid();
    const stacticsPaidOnCardInt = await removeCommas(stacticsPaid);
    const caculatedPaid = SpecDataManager.get('productOfPaid');
    expect(parseInt(stacticsPaidOnCardInt)).toBe(caculatedPaid);
  }
  //product

  //sum
  @Step(
    'Web: Verify the total amount on card is the same as sum of data on column <col>',
  )
  public async getDataFromCellsTotal(col: string) {
    const cellHeader = await (await Finance.getInstance()).countMatCellHeader();
    const headerTexts = [];
    for (const headerCell of await cellHeader.all()) {
      const text = await headerCell.textContent();
      headerTexts.push(text.trim());
    }
    const totalAmountHeaderNth = headerTexts.indexOf(col);
    const rows = await (await Finance.getInstance()).countMatRow();
    const rowCount = await rows.count();
    let sumOfDataTotal = 0;
    for (let i = 0; i < rowCount; i++) {
      const row = rows.nth(i);
      const cell = row.locator('mat-cell').nth(totalAmountHeaderNth);
      const cellText = await removeCommas(cell);
      sumOfDataTotal = sum(sumOfDataTotal, parseInt(cellText));
    }
    const totalAmountOnCard = await (
      await Finance.getInstance()
    ).totalAmountCard();
    const totalAmountOnCardInt = await removeCommas(totalAmountOnCard);
    expect(parseInt(totalAmountOnCardInt)).toBe(sumOfDataTotal);
  }

  @Step(
    'Web: Verify the unpaid amount on card is the same as sum of data on column <col>',
  )
  public async getDataFromCellsUnPaid(col: string) {
    const cellHeader = await (await Finance.getInstance()).countMatCellHeader();
    const headerTexts = [];
    for (const headerCell of await cellHeader.all()) {
      const text = await headerCell.textContent();
      headerTexts.push(text.trim());
    }
    const unpaidAmountHeaderNth = headerTexts.indexOf(col);
    const rows = await (await Finance.getInstance()).countMatRow();
    const rowCount = await rows.count();
    let sumOfDataUnpaid = 0;
    for (let i = 0; i < rowCount; i++) {
      const row = rows.nth(i);
      const cell = row.locator('mat-cell').nth(unpaidAmountHeaderNth);
      const cellText = await removeCommas(cell);
      sumOfDataUnpaid = sum(sumOfDataUnpaid, parseInt(cellText));
    }
    const unpaidAmountOnCard = await (
      await Finance.getInstance()
    ).totalAmountUnpaidCard();
    const unpaidAmountOnCardInt = await removeCommas(unpaidAmountOnCard);
    expect(parseInt(unpaidAmountOnCardInt)).toBe(sumOfDataUnpaid);
  }

  @Step(
    'Web: Verify the paid amount on card is the same as sum of data on column <col>',
  )
  public async getDataFromCellsPaid(col: string) {
    const cellHeader = await (await Finance.getInstance()).countMatCellHeader();
    const headerTexts = [];
    for (const headerCell of await cellHeader.all()) {
      const text = await headerCell.textContent();
      headerTexts.push(text.trim());
    }
    const paidAmountHeaderNth = headerTexts.indexOf(col);
    const rows = await (await Finance.getInstance()).countMatRow();
    const rowCount = await rows.count();
    let sumOfDataPaid = 0;
    for (let i = 0; i < rowCount; i++) {
      const row = rows.nth(i);
      const cell = row.locator('mat-cell').nth(paidAmountHeaderNth);
      const cellText = await removeCommas(cell);
      sumOfDataPaid = sum(sumOfDataPaid, parseInt(cellText));
    }
    const paidAmountOnCard = await (
      await Finance.getInstance()
    ).totalAmountPaidCard();
    const paidAmountOnCardInt = await removeCommas(paidAmountOnCard);
    expect(parseInt(paidAmountOnCardInt)).toBe(sumOfDataPaid);
  }
  //sum
}
