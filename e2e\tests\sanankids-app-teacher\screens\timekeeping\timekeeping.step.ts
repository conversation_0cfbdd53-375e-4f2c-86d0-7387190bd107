import { Step } from 'gauge-ts';
import { TimekeepingScreen } from './timekeeping.screen';

export class TimekeepingStep {
  @Step('Mobile: Click button timekeeping on today screen')
  public async clickButtonTimekeeping() {
    const elem = await (
      await TimekeepingScreen.getInstance()
    ).timekeepingButton();
    await elem.waitForDisplayed();
    await elem.click();
  }

  @Step('Mobile: Click sys button on timekeeping screen')
  public async clickSysButton() {
    const elem = await (
      await TimekeepingScreen.getInstance()
    ).sysButton();
    await elem.waitForDisplayed();
    await elem.click();
  }
}