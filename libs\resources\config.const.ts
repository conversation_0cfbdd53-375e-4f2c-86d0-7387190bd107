export const IsHeadless = !!process.env.IS_HEADLESS;

export const IsMobile = !!process.env.IS_MOBILE;

export const ViewportWidth =
  parseInt(process.env.PLAYWRIGHT_VIEWPORT_WIDTH || '0') || 1280;
export const ViewportHeight =
  parseInt(process.env.PLAYWRIGHT_VIEWPORT_HEIGHT || '0') || 720;

export const SuiteBrowserKey = 'suite_browser';
export const SpecContextKey = 'spec_context';
export const PinnedLocatorKey = 'step_pinned_locator';
export const SuiteAppiumDriverKey = 'suite_appium_driver';

export const androidCapabilities = {
  'appium:platformName': process.env.PLATFORM_NAME || 'Android',
  'appium:automationName': process.env.AUTOMATION_NAME || 'UiAutomator2',
  'appium:platformVersion': process.env.PLATFORM_VERSION,
  'appium:deviceName': process.env.DEVICE_NAME,
  'appium:appPackage': process.env.APP_PACKAGE,
  'appium:appActivity': process.env.APP_ACTIVITY,
};

export const iosCapabilities = {
  'appium:platformName': process.env.IOS_PLATFORM_NAME || 'iOS',
  'appium:automationName': process.env.IOS_AUTOMATION_NAME || 'XCUITest',
  'appium:deviceName': process.env.IOS_DEVICE_NAME,
  'appium:udid': process.env.IOS_UDID,
  'appium:platformVersion': process.env.IOS_PLATFORM_VERSION,
  'appium:app': process.env.IOS_APP,
};