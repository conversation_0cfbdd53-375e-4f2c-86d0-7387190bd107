package AppParentModules.Message.Locators;

import org.openqa.selenium.By;

public class MessageLocators {
    // Click tab [Tin nhắn]
    public static By tabMessage = By.xpath("//android.widget.Button[@content-desc=\"Tin nhắn, tab, 3 of 5\"]");

    // Click to open chat [Nhà trường]
    public static By messSchool = By.xpath("//android.widget.TextView[@text=\"Nhà trường Cơ sở TH\"]");

    // Click edit text [Nhập tin nhắn]
    public static By textboxMess = By.xpath("//android.widget.EditText[@text=\"Nhập tin nhắn ...\"]");

    // Click icon arrow
    public static By iconArrow = By.xpath("//android.widget.FrameLayout[@resource-id=\"android:id/content\"]/android.widget.FrameLayout/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup[2]/android.view.ViewGroup/android.view.ViewGroup/android.widget.FrameLayout/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.widget.FrameLayout/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup[2]/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup[3]/android.view.ViewGroup/android.view.ViewGroup[4]/com.horcrux.svg.SvgView/com.horcrux.svg.l/com.horcrux.svg.l/com.horcrux.svg.u[1]");

    // CLick icon Back
    public static By iconBack = By.xpath("//android.widget.FrameLayout[@resource-id=\"android:id/content\"]/android.widget.FrameLayout/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup[2]/android.view.ViewGroup/android.view.ViewGroup/android.widget.FrameLayout/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.widget.FrameLayout/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup[2]/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup[1]/android.view.ViewGroup/android.view.ViewGroup[1]/com.horcrux.svg.SvgView/com.horcrux.svg.l/com.horcrux.svg.u");

}
