# SANAN Kid - Web Admin - Perform create, edit, and delete actions for a meal - Meal

tags: web,mobile,sanankid-web-admin,MQAA

## 000005

### Login page
* Login sanankid web with "0936275212" and "27041998", then select profile "autotest_001" of school "Trường Trung Tâm Automation Testing"
### Verify meal modification for multiple blocks success
* Web: Click on menu "Ăn uống" on sidebar
* Web: Click on item "Cài đặt bữa ăn" on menu
* Web: Click button with "Thêm mới"
* Web: Click the apply block checkbox "gradle_001" on the meal settings screen
* Web: Click the apply block checkbox "gradle_002" on the meal settings screen
* Web: Enter "dish_test" into dish name field in add dish form
* Web: Enter "07:00" into the time to eat field in add dish form
* Web: Enter "10000" into the cost field in add dish form
* Web: Click the add button in the add dish form
* Web: Verify exact "Thông tin đã được thêm mới." is visible
* Web: On row that has "dish_test", doing step:
* Web: => On column "Tác vụ" click on "edit"
* Web: Enter "dish_test_edited" into dish name field in add dish form
* Web: Click the add button in the add dish form
* Web: Verify exact "Thông tin đã được cập nhật." is visible
* Web: On row that has "dish_test_edited", doing step:
* Web: => Verify on column "Tên bữa ăn" has "dish_test_edited"
* Web: On row that has "dish_test_edited", doing step:
* Web: => On column "Tác vụ" click on "delete"
* Web: Click button with "Đồng ý"
* Web: Verify exact "Thông tin đã được xóa." is visible
* Web: Click item "gradle_002" on tab
* Web: On row that has "dish_test", doing step:
* Web: => On column "Tác vụ" click on "edit"
* Web: Enter "dish_test_edited" into dish name field in add dish form
* Web: Click the add button in the add dish form
* Web: Verify exact "Thông tin đã được cập nhật." is visible
* Web: On row that has "dish_test_edited", doing step:
* Web: => Verify on column "Tên bữa ăn" has "dish_test_edited"
* Web: On row that has "dish_test_edited", doing step:
* Web: => On column "Tác vụ" click on "delete"
* Web: Click button with "Đồng ý"
* Web: Verify exact "Thông tin đã được xóa." is visible
* Web: Logout sanankid web admin
