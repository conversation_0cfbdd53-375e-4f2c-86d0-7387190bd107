import { getAppiumDriver } from '@libs/resources/appium.resource';
import { Step } from 'gauge-ts';
import { LoginScreen } from './login.screen';

export class LoginScreenSteps {
  @Step('Mobile: Open sanankid app teacher')
  public async openLogInPage() {
    await getAppiumDriver();
  }

  @Step('Mobile: Fill username input <username> in app teacher')
  public async fillUserName(username: string) {
    const usernameInput = await (
      await LoginScreen.getInstance()
    ).usernameInput();
    await usernameInput.waitForDisplayed({ timeout: 60000 });
    await usernameInput.setValue(username);
  }

  @Step('Mobile: Click button continue to fill password')
  public async clickButtonContinue() {
    const continueBtn = await (
      await LoginScreen.getInstance()
    ).continueButton();
    await continueBtn.waitForDisplayed();
    await continueBtn.click();
  }

  @Step('Mobile: Fill password input <password> in app teacher')
  public async fillPassword(password: string) {
    const passwordInput = await (
      await LoginScreen.getInstance()
    ).passwordInput();
    await passwordInput.waitForDisplayed();
    await passwordInput.setValue(password);
  }

  @Step('Mobile: Click button allow permission controler')
  public async clickButtonAllowPermissionControler() {
    const mediaBtn = await (
      await LoginScreen.getInstance()
    ).permissionControlerMedia();
    await mediaBtn.waitForDisplayed();
    await mediaBtn.click();

    const filesBtn = await (
      await LoginScreen.getInstance()
    ).permissionControlerFiles();
    await filesBtn.waitForDisplayed();
    await filesBtn.click();
  }
}
