# SANAN Kid - Web Admin - Sidebar - Finance

tags: web,sanankid-web-admin,FQAC

## FQAC_000002

### Login page
* Login sanankid web with "0854124589" and "27041998", then select profile "Automation Test" of school "Trường Trườ<PERSON>"

### Input data
* Web: Click on menu "Tài chính" on sidebar
* Web: Click on item "Thiết lập mức thu" on menu

## Verify data
|newFeeName|
|TestFee|
|232177|
|<br>test</br>|
|a' or '1'='1|
|@#$%^&*|
* Web: Click button with "Thêm mới"
* Web: Click with "Thêm mới đối tượng áp dụng" exact
* Fill fee name <newFeeName>, fee "1000000" and unit "112" to add new fee
* Web: Click button with "Thêm mới"
* Web: Verify exact <newFeeName> is on the student fee table
* Web: On row that has <newFeeName>, doing step:
* Web: => Click on edit button (button 2) on column "Tác vụ"
* Web: Click button with "Đồng ý" exact
* Wait "2" second