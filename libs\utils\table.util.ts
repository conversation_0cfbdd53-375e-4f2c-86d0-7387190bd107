import { Locator } from 'playwright';

export class DOMTable {
  public constructor(public readonly table: Locator) {}
}

export class DOMTableRow {
  public constructor(public readonly row: Locator) {}

  public async getParentTable() {
    const datatable = this.row
      .page()
      .locator('div[role="table"]', { has: this.row });
    const htmlTable = this.row.page().locator('table', { has: this.row });
    const matTable = this.row.page().locator('mat-table', { has: this.row });
    try {
      await Promise.all([
        datatable.waitFor({ timeout: 1000 }),
        htmlTable.waitFor({ timeout: 1000 }),
        matTable.waitFor({ timeout: 1000 }),
      ]);
    } catch (e) {}

    const countDataTable = await datatable.count();
    const countHtmlTable = await htmlTable.count();
    const countMatTable = await matTable.count();
    if (countDataTable === 1) {
      return datatable;
    } else if (countHtmlTable === 1) {
      return htmlTable;
    } else if (countMatTable === 1) {
      return matTable;
    }

    throw new Error('table not found');
  }
}

export class TableUtil {
  public static async getColumnIndexByCaption(
    caption: string,
    trOrTable: DOMTable | DOMTableRow,
  ): Promise<number> {
    const table =
      trOrTable instanceof DOMTable
        ? trOrTable.table
        : await trOrTable.getParentTable();
    const headerCols = await table.getByRole('columnheader').all();
    for (let i = 0; i < headerCols.length; i++) {
      if ((await headerCols[i].getByText(caption).count()) !== 0) {
        return i;
      }
    }
    throw new Error(`${caption} not found`);
  }
}
