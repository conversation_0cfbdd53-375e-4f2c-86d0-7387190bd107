import { LogStartEnd } from '@libs/decorators/log.decorator';
import { Step } from 'gauge-ts';
import { expect } from 'playwright/test';
import { Header } from './header.component';
import { NotificationDropDownMenu } from './notification-drop-down-menu.component';

export class NotificationDropDownMenuSteps {
  @LogStartEnd()
  @Step('Web: On notification drop-down menu in the header:')
  public async pinProfileDropDownMenu() {
    const elem = await (await Header.getInstance()).notiDropDownMenu();
    NotificationDropDownMenu.setLocator(elem);
  }

  @LogStartEnd()
  @Step('Web: => Check text <text> is displayed in noti box')
  public async clickProfileByName(text: string) {
    const elem = await NotificationDropDownMenu.getInstance().getElementByText(
      text,
    );
    await expect(elem).toContainText(text);
  }
}
