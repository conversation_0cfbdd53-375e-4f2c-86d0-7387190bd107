import { WebPageDefaultIndex } from '@libs/constants/config.const';
import { getSpecPage } from '@libs/resources/playwright.resource';
import { WebResource } from '@libs/resources/web.resource';
import { Page } from 'playwright';

export abstract class PageBase extends WebResource {
  protected constructor(public page: Page) {
    super(page);
  }

  public static async getInstance<T extends WebResource>(
    this: new (page: Page) => T,
    index: number = WebPageDefaultIndex,
  ): Promise<T> {
    return new this(await getSpecPage(index));
  }
}
