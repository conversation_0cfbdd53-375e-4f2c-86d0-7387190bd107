import { Step } from 'gauge-ts';
import { expect } from 'playwright/test';
import { Sidebar } from './sidebar.component';

export class SidebarSteps {
  @Step('Web: Click on menu <menuName> on sidebar')
  public async clickOnMenu(menuName: string) {
    const elem = await (await Sidebar.getInstance()).menuByText(menuName);
    await elem.click();
  }

  @Step('Web: Click on item <itemName> on menu')
  public async clickOnItem(itemName: string) {
    const elem = await (await Sidebar.getInstance()).itemByText(itemName);

    const count = await elem.count();

    if (count > 1) {
      await elem.last().click();
    } else {
      await elem.click();
    }
  }

  @Step('Web: Verify page title is <title>')
  public async verifyPageHeader(header: string) {
    const elem = await (await Sidebar.getInstance()).pageHeader(header);
    await expect(elem).toBeVisible();
  }
}
