import { DataStore } from 'gauge-ts';

export abstract class DataManager {
  protected static factory: () => DataStore;

  private static get dataStore(): DataStore {
    return this.factory();
  }

  public static get<T>(key: string): T {
    const value = this.dataStore.get(key);
    if (value === undefined) {
      throw new Error(`key: ${key} is not found`);
    }

    return value as T;
  }

  public static put<T>(key: string, value: T): T {
    this.dataStore.put(key, value);
    return value;
  }

  public static has(key: string): boolean {
    try {
      return !!this.get(key);
    } catch {
      return false;
    }
  }
}
