package AppParentModules.Message.Actions;

import AppParentModules.Message.Locators.MessageLocators;
import Base.PageObjectBase;
import Utilities.Log;
import io.appium.java_client.MobileDriver;
import io.appium.java_client.MobileElement;
import org.openqa.selenium.By;

public class Message extends PageObjectBase {

    public Message(MobileDriver<MobileElement> appiumDriver) {
        super(appiumDriver);
    }

    public void sendMessage(By user, String userName,String message){
        clickOnElement(MessageLocators.tabMessage, "tab message");
        clickOnElement(user, "nhan voi " + userName);
        formInputByLocator(MessageLocators.textboxMess, message);
        clickOnElement(MessageLocators.iconArrow, "nhan de gui tin nhan");
        goBack();
    }

    public void goBack() {
        MobileElement mobileElement = waitForElementPresence(MessageLocators.iconBack, MAX_WAIT_TIME);
        while (!isElementClicked(mobileElement)) {
            mobileElement.click();
            Log.info("Go back");
        }
    }
}
