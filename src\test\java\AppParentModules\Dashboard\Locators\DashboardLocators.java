package AppParentModules.Dashboard.Locators;

import org.openqa.selenium.By;

public class DashboardLocators {
    // Click tab [Bảng tin]
    public static By tabNewsFeed = By.xpath("//android.widget.Button[@content-desc=\"Bảng tin, tab, 2 of 5\"]");

    // Click tab [Nhà trường]
    public static By tabSchool = By.xpath("//android.widget.TextView[@text=\"Nhà Trường\"]");

    // Click icon heart
    public static By iconHeart = By.xpath("//android.widget.HorizontalScrollView/android.view.ViewGroup/android.view.ViewGroup[1]/android.view.ViewGroup/android.view.ViewGroup[4]/android.view.ViewGroup[2]/android.view.ViewGroup[1]/android.view.ViewGroup/android.view.ViewGroup/com.horcrux.svg.SvgView/com.horcrux.svg.l/com.horcrux.svg.u");


}
