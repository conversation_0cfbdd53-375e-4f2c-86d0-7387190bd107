package AppParentModules.Dashboard.Actions;

import AppParentModules.Message.Locators.MessageLocators;
import Base.PageObjectBase;
import Utilities.Log;
import io.appium.java_client.MobileDriver;
import io.appium.java_client.MobileElement;
import org.openqa.selenium.interactions.touch.TouchActions;

public class Dashboard extends PageObjectBase {
    public Dashboard(MobileDriver<MobileElement> appiumDriver) {
        super(appiumDriver);
    }

    public void clickOnButtonComment(){}

    public void clickToCommentOnPost(){}

    public void clickToGiveHeartOnPost(){}

    public void clickToClearHeartOnPost(){}

    public void scrollVerticalToPost(MobileElement element, int x, int y){
        TouchActions actions = new TouchActions(appiumDriver);
        actions.scroll(element, x, y);
        actions.perform();
    }

    public void scrollHorizontalToPost(){}

    public void swipeVerticalToPost(){}

    public void swipeHorizontalToPost(){}

    public void goBack() {
        MobileElement mobileElement = waitForElementPresence(MessageLocators.iconBack, MAX_WAIT_TIME);
        while (!isElementClicked(mobileElement)) {
            mobileElement.click();
            Log.info("Go back");
        }
    }
}
