# Sent invoice on row has <name1>,<name2> and verify value is <value>
### Input data
* Web: On row that has <name1>, click the checkbox in the first cell
* Web: On row that has <name2>, click the checkbox in the first cell
* Web: Click button with "Gửi phiếu thu"
* Web: Click button with "Đồng ý"
* Wait "2" second
### Verify data
* Web: Open tab "Phiếu thu đã gửi" in on new collection period
* Web: On row that has <name1>, doing step:
* Web: => Verify on column "Fees" has value <value>
* Web: On row that has <name2>, doing step:
* Web: => Verify on column "Fees" has value <value>
* Web: Open tab "Xác nhận thanh toán" in on new collection period
* Web: Wait for page stable
* Web: On row that has <name1>, doing step:
* Web: => Verify on column "Còn thiếu" has value <value>
* Web: On row that has <name2>, doing step:
* Web: => Verify on column "Còn thiếu" has value <value>
* Web: Open tab "<PERSON><PERSON>c nhận thanh toán" in on new collection period
* Web: Wait for page stable
* Web: Verify the total amount on card is the same as sum of data on column "Tổng thu"
* Web: Verify the unpaid amount on card is the same as sum of data on column "Còn thiếu"