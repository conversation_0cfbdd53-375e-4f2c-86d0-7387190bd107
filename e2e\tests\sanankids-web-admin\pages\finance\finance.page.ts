import { PageBase } from '@libs/bases/web/page.base';
import { TableBase } from '@libs/bases/web/table.base';
import { LocatorGetter } from '@libs/decorators/locator.decorator';
import { Page } from 'playwright';

export class Finance extends PageBase {
  public constructor(page: Page) {
    super(page);
  }

  @LocatorGetter()
  public async errorNameIsVisible(errorName: string) {
    return this.getElementByCssSelector('#error-name').getByText(errorName);
  }

  @LocatorGetter()
  public async errorValueIsVisible(errorValue: string) {
    return this.getElementByCssSelector('#error-value-0').getByText(errorValue);
  }

  @LocatorGetter()
  public async errorUnitIsVisible(errorUnit: string) {
    return this.getElementByCssSelector('#error-unit-0').getByText(errorUnit);
  }

  @LocatorGetter()
  public async feeInput() {
    return this.getElementByCssSelector('#value-0');
  }

  @LocatorGetter()
  public async unitInput() {
    return this.getElementByCssSelector('#unit-0');
  }

  @LocatorGetter()
  public async locateFeeTable() {
    return this.getElementByCssSelector('#student-fee-table');
  }

  @LocatorGetter()
  public async newFeeNameIsVisible(newFeeName: string) {
    return (await this.locateFeeTable()).getByText(newFeeName);
  }

  @LocatorGetter()
  public async deleteNameInput() {
    return this.getElementByCssSelector('#name');
  }

  @LocatorGetter()
  public async setScopeMenu(scopeMenu: string) {
    return this.getElementByCssSelector('#scope-0').getByText(scopeMenu);
  }

  @LocatorGetter()
  public async setScope(scope: string) {
    return this.getElementByCssSelector('#scope-0-panel').getByText(scope);
  }

  @LocatorGetter()
  public async disableButton() {
    return this.getElementByCssSelector('#submit-btn');
  }

  @LocatorGetter()
  public async gradeDropdownMenu() {
    return this.getElementByCssSelector('#gradeids-0');
  }

  @LocatorGetter()
  public async setGrade(grade: string) {
    return this.getElementByCssSelector('.mat-option-text').getByText(grade);
  }

  @LocatorGetter()
  public async classDropdownMenu() {
    return this.getElementByCssSelector('#classids-0');
  }

  @LocatorGetter()
  public async setClass(className: string) {
    return this.getElementByCssSelector('.mat-option-text').getByText(
      className,
    );
  }

  @LocatorGetter()
  public async searchStudentBar() {
    return this.getElementByCssSelector('#studentids-0');
  }

  @LocatorGetter()
  public async searchBar() {
    return this.getElementByCssSelector('#name-search');
  }

  @LocatorGetter()
  public async removeButton() {
    return this.getElementByCssSelector('#remove-0');
  }
  //25,26,29
  @LocatorGetter()
  public async deleteButton() {
    return this.getElementByCssSelector('#delete-1');
  }

  @LocatorGetter()
  public async createButton() {
    return this.getElementByCssSelector('#add-fee-btn');
  }
  //25,26,29
  @LocatorGetter()
  public async locateTab() {
    return this.getElementByCssSelector('.mat-tab-header');
  }

  @LocatorGetter()
  public async locateSecondScope() {
    return this.getElementByCssSelector('#scope-1');
  }

  @LocatorGetter()
  public async setSecondScope(scope: string) {
    return this.getElementByCssSelector('#scope-1-panel').getByText(scope);
  }

  @LocatorGetter()
  public async tab(tabItem: string) {
    return this.getElementByCssSelector('.mat-tab-header').getByText(tabItem);
  }

  @LocatorGetter()
  public async locateLatePickUpFee() {
    return this.getElementByCssSelector('#value-1');
  }

  @LocatorGetter()
  public async locateLatePickUpStart() {
    return this.getElementByCssSelector('#startminute-1');
  }

  @LocatorGetter()
  public async locateLatePickUpEnd() {
    return this.getElementByCssSelector('#endminute-1');
  }
  //25,26,29
  //23
  @LocatorGetter()
  public async locateFeeMeal() {
    return this.locator
      .getByText('Brunch')
      .locator('../..')
      .locator('.col-3')
      .locator('input');
  }
  //23
  //collection period

  @LocatorGetter()
  public async addNewCollectionPeriodPopup() {
    return this.getElementByCssSelector(
      'div > div > mat-dialog-container',
    ).first();
  }

  @LocatorGetter()
  public async addNewFeePopup() {
    return this.getElementByCssSelector(
      'div > div > mat-dialog-container',
    ).last();
  }

  @LocatorGetter()
  public async locateTabOnNewCollectionPeriod() {
    return this.getElementByCssSelector('.mat-tab-list');
  }

  @LocatorGetter()
  public async tabIsVisible(tab: string) {
    return (await this.locateTabOnNewCollectionPeriod()).getByText(tab);
  }

  @LocatorGetter()
  public async saveSheetTable() {
    return this.getElementByCssSelector('#save-sheet-table');
  }

  @LocatorGetter()
  public async confirmPayment() {
    return this.getElementByCssSelector('#confirm-payment-btn');
  }

  @LocatorGetter()
  public async openTab(name: string) {
    return (await this.locateTabOnNewCollectionPeriod()).getByText(name)
  }

  @LocatorGetter()
  public async totalAmountCol(value: string) {
    return this.getElementByCssSelector('#col-amount').getByText(value);
  }

  @LocatorGetter()
  public async totalAmountCard() {
    return this.getElementByCssSelector('#total-amount');
  }

  @LocatorGetter()
  public async totalAmountUnpaidCard() {
    return this.getElementByCssSelector('#total-amount-unpaid');
  }

  @LocatorGetter()
  public async totalAmountPaidCard() {
    return this.getElementByCssSelector('#total-amount-paid');
  }

  @LocatorGetter()
  public async totalInvoice() {
    return this.getElementByCssSelector('#total-invoice > span');
  }

  @LocatorGetter()
  public async totalPaidInvoice() {
    return this.getElementByCssSelector('#total-invoice-paid > span');
  }

  @LocatorGetter()
  public async totalUnpaidInvoice() {
    return this.getElementByCssSelector('#total-invoice-unpaid > span');
  }

  @LocatorGetter()
  public async countMatRow() {
    return this.getElementByCssSelector('mat-row');
  }

  @LocatorGetter()
  public async countMatCellHeader() {
    return this.getElementByCssSelector('mat-header-cell');
  }

  @LocatorGetter()
  public async statisticPaid() {
    return this.getElementByCssSelector('#statistic-paid');
  }

  @LocatorGetter()
  public async statisticExpected() {
    return this.getElementByCssSelector('#statistic-expected');
  }

  @LocatorGetter()
  public async statisticUnpaid() {
    return this.getElementByCssSelector('#statistic-unpaid');
  }

}
