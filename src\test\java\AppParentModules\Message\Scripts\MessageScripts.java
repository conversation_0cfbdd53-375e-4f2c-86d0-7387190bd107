package AppParentModules.Message.Scripts;

import AppParentModules.Login.Locators.LoginLocators;
import AppParentModules.Message.Locators.MessageLocators;
import Common.TestScriptBase;
import org.testng.annotations.Test;

public class MessageScripts extends TestScriptBase {
    @Test
    public void sendMessage(){
        loginPage.doLogin();
        loginPage.clickChooseProfile(LoginLocators.profile1, "Be duc");
        messagePage.sendMessage(MessageLocators.messSchool, "<PERSON>ha truong","xin chao viet nam");
        base.CaptureScreenShot("appiumDriver");
    }
}
