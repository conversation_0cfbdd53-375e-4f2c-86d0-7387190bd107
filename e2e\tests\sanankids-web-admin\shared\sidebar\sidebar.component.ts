import { LocatorGetter } from '@libs/decorators/locator.decorator';
import { PageBase } from '@libs/bases/web/page.base';
import { Page } from 'playwright';

export class Sidebar extends PageBase {
  public constructor(page: Page) {
    super(page);
  }

  @LocatorGetter()
  public async sideBar() {
    return this.page.locator('.sidenav');
  }

  @LocatorGetter()
  public async menuByText(text: string) {
    return (await this.sideBar()).getByText(text, { exact: true });
  }

  @LocatorGetter()
  public async itemByText(text: string) {
    return (await this.sideBar()).getByText(text, { exact: true });
  }

  @LocatorGetter()
  public async locatePageHeader() {
    return this.getElementByCssSelector('.page-header');
  }

  @LocatorGetter()
  public async pageHeader(header: string) {
    return (await this.locatePageHeader()).getByText(header);
  }
}
