package AppParentModules.Dashboard.Scripts;

import AppParentModules.Login.Locators.LoginLocators;
import Common.TestScriptBase;
import org.testng.annotations.Test;

public class DashboardScripts extends TestScriptBase {
    @Test
    public void commentOnPost(){
        loginPage.doLogin();
        base.CaptureScreenShot("Login");
        loginPage.clickChooseProfile(LoginLocators.profile1, "Be duc");
//        dashboardPage.clickOnButtonComment();
//        base.CaptureScreenShot("Comment on post");
//        dashboardPage.goBack();
//        dashboardPage.clickToCommentOnPost();
//        dashboardPage.clickToClearHeartOnPost();
////        base.CaptureScreenShot("Clear heart icon");
//        dashboardPage.clickToClearHeartOnPost();
//        base.CaptureScreenShot("Clear heart icon");
//        dashboardPage.clickToGiveHeartOnPost();
//        base.CaptureScreenShot("Give heart to post");
    }
}
