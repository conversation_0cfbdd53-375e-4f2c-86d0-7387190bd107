import { ElementBase } from '@libs/bases/web/element.base';
import { LocatorGetter } from '@libs/decorators/locator.decorator';
import { Locator } from 'playwright';

export class AddNewCollectionPeriod extends ElementBase {
  public constructor(page: Locator) {
    super(page);
  }
  @LocatorGetter()
  public async collectionPeriodName() {
    return this.getElementByCssSelector('#name');
  }

  @LocatorGetter()
  public async locateCollectionPeriodScope() {
    return this.getElementByCssSelector('.mat-select-placeholder');
  }

  @LocatorGetter()
  public async checkbox() {
    return this.getElementByCssSelector('#checkbox-0');
  }
}
