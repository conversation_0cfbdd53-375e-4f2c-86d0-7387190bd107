import { Step } from 'gauge-ts';
import { CheckingAttendanceScreen } from './checking.attendance.screen';
import { getAppiumDriver } from '@libs/resources/appium.resource';

export class CheckingAttendanceSteps {

  @Step('Mobile: Click checkbox attendance on checking attendance screen')
  public async clickCheckboxAttendance() {
    const elem = await (
      await CheckingAttendanceScreen.getInstance()
    ).rollBackAttendance();
    await elem.waitForDisplayed();
    await elem.click();
  }

  @Step('Mobile: Verify checkbox attendance is checked on checking attendance screen')
  public async verifyCheckboxAttendance() {
    const elem = await (
      await CheckingAttendanceScreen.getInstance()
    ).rollBackAttendance();
    await elem.waitForDisplayed();
    await elem.isEnabled();
  }

  @Step('Mobile: Close and relunch teacher app')
  public async closeApp() {
    const driver = await getAppiumDriver();
    // Terminate the app (close the app)
    await driver.execute('mobile: terminateApp', {
      appId: 'com.sanankids.teacher.dev',
    });

    // Activate the app again (relaunch the app)
    await driver.execute('mobile: activateApp', {
      appId: 'com.sanankids.teacher.dev',
    });
  }

  @Step('Mobile: Click button attendance on today screen')
  public async clickButtonAttendance() {
    const elem = await (
      await CheckingAttendanceScreen.getInstance()
    ).attendanceButton();
    await elem.waitForDisplayed();
    await elem.click();
  }

  @Step('Mobile: Verify message attendance on checking attendance screen')
  public async verifyMessageAttendanceSucess() {
    const elem = await (
      await CheckingAttendanceScreen.getInstance()
    ).messageAttendanceSucess();
    await elem.isDisplayed();
  }

  @Step('Mobile: Click on capture checkin attendance on checking attendance screen')
  public async clickCaptureCheckinAttendance() {
    const elem = await (
      await CheckingAttendanceScreen.getInstance()
    ).captureCheckinAttendance();
    await elem.waitForDisplayed();
    await elem.click();
  }

  @Step('Mobile: Click button capture on checking attendance screen')
  public async clickButtonCapture() {
    const elem = await (
      await CheckingAttendanceScreen.getInstance()
    ).buttonCapture();
    await elem.waitForDisplayed();
    await elem.click();
  }
}