# SANAN Kid - Web Admin - Decentralization - Meal

tags: web,mobile,sanankid-web-admin,MQAA

## 000005_1

### Login page
* Login sanankid web with "0854124589" and "27041998", then select profile "Quản lý toàn trường" of school "Trường Trung Tâm Automation Testing"
### edit meal list 
* Web: Click on menu "Ăn uống" on sidebar
* Web: Click on item "Danh sách món ăn" on menu
* Web: Click button with "Thêm mới"
* Web: Input "create_dish" into field with id "[id^='mat-input-']" index "0"
* Web: Click button with "Thêm mới"
* Web: Logout sanankid web admin

## 000005_2

### Login page
* Login sanankid web with "0936275212" and "27041998", then select profile "autotest_001" of school "Trường Trung Tâm Automation Testing"
### edit meal list 
* Web: Click on menu "Ăn uống" on sidebar
* Web: Click on item "Danh sách món ăn" on menu
* Web: Verify exact "Thêm mới" is visible
* Web: On row that has "dish_001", doing step:
* Web: => Verify on column "Món ăn" has "dish_001"
* Web: On row that has "dish_002", doing step:
* Web: => Verify on column "Món ăn" has "dish_002"
* Web: On row that has "dish_003", doing step:
* Web: => Verify on column "Món ăn" has "dish_003"
* Web: Logout sanankid web admin
