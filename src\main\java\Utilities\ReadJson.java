package Utilities;

import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.google.gson.stream.JsonReader;

import java.io.FileNotFoundException;
import java.io.FileReader;
import java.lang.reflect.Type;
import java.util.List;

public class ReadJson {
    public static Device getDevice() throws FileNotFoundException {
        Device currentDevice = new Device();
        Type DEVICE_TYPE = new TypeToken<List<Device>>(){}.getType();
        Gson gson = new Gson();
        JsonReader jsonReader = new JsonReader(new FileReader("src/test/resources/setup_devices.json"));
        List<Device> deviceList = gson.fromJson(jsonReader, DEVICE_TYPE);
        for (Device device : deviceList) {
            if (device.deviceStatus) {
                currentDevice = device;
                break;
            }
        }
        return currentDevice;
    }
}
