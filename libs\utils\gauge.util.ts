import { SpecInfo } from '@libs/gauge/spec-info';
import {
  ScenarioNameKey,
  SpecInfoKey,
  StepTitleKey,
} from 'libs/constants/global.const';
import { SpecDataManager } from '@libs/gauge/spec.data-manager';

export const getCurrentScenarioName = () => {
  return SpecDataManager.has(ScenarioNameKey)
    ? SpecDataManager.get<string>(ScenarioNameKey)
    : '';
};

export const getCurrentSpecID = () => {
  return getCurrentSpecInfo().specID;
};

export const getCurrentSpecInfo = () => {
  return SpecDataManager.has(SpecInfoKey)
    ? SpecDataManager.get<SpecInfo>(SpecInfoKey)
    : null;
};

export const getCurrentStepTitle = () => {
  return SpecDataManager.get<string>(StepTitleKey);
};
