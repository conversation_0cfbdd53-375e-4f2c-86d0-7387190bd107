package Common;

import AppParentModules.Dashboard.Actions.Dashboard;
import AppParentModules.Login.Actions.Login;
import AppParentModules.Message.Actions.Message;
import Base.PageObjectBase;
import Manager.AppiumDriverManager;
import Utilities.ReadJson;
import io.appium.java_client.MobileDriver;
import io.appium.java_client.MobileElement;
import org.testng.ITestResult;
import org.testng.annotations.*;


import java.io.FileNotFoundException;

import static Utilities.VideoRecorder.startRecording;
import static Utilities.VideoRecorder.stopRecording;

public class TestScriptBase {
    protected static MobileDriver<MobileElement> appiumDriver;
    protected Login loginPage;
    protected Message messagePage;
    protected Dashboard dashboardPage;
    protected PageObjectBase base;

    @BeforeMethod
    public void beforeSetup() throws FileNotFoundException {
        if(ReadJson.getDevice().getDeviceOS().equals("Android")){
            appiumDriver = AppiumDriverManager.getAndroidDriver();
        }
        else {
            appiumDriver = AppiumDriverManager.getIOSDriver();
        }
        loginPage = new Login(appiumDriver);
        messagePage = new Message(appiumDriver);
        dashboardPage = new Dashboard(appiumDriver);
        base = new PageObjectBase(appiumDriver);
        startRecording(appiumDriver);
    }

    @AfterMethod
    public void afterSetup(ITestResult result) throws InterruptedException {
        Class<?> testClass = result.getTestClass().getRealClass();
        String nameClass = testClass.getSimpleName();
        Thread.sleep(5000);
        stopRecording(nameClass, appiumDriver);
        appiumDriver.quit();
    }

}
