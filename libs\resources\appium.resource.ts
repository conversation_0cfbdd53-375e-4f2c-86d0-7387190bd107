import { SuiteDataManager } from '@libs/gauge/suite.data.manager';
import { remote, RemoteOptions } from 'webdriverio';
import { androidCapabilities, iosCapabilities, SuiteAppiumDriverKey } from './config.const';

export type AppiumDriver = WebdriverIO.Browser;

const options: RemoteOptions = {
  hostname: process.env.APPIUM_HOST || 'localhost',
  port: parseInt(process.env.APPIUM_PORT, 10) || 4723,
  logLevel: 'silent',
  capabilities: process.env.PLATFORM_NAME === 'Android' ? iosCapabilities : androidCapabilities,
};

export const getAppiumDriver = async (): Promise<AppiumDriver> => {
  try {
    return SuiteDataManager.get<AppiumDriver>(SuiteAppiumDriverKey);
  } catch (e) {
    const driver: AppiumDriver = await remote(options);
    SuiteDataManager.put(SuiteAppiumDriverKey, driver);
    return driver;
  }
};

export const terminateDriver = async () => {
  try {
    const driver: AppiumDriver =
      SuiteDataManager.get<AppiumDriver>(SuiteAppiumDriverKey);
    await driver.deleteSession();
  } catch (e) {}
};
