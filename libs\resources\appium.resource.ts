import { SuiteDataManager } from '@libs/gauge/suite.data.manager';
import { remote, RemoteOptions } from 'webdriverio';
import {
  androidCapabilities,
  iosCapabilities,
  SuiteAppiumDriverKey,
} from './config.const';

export type AppiumDriver = WebdriverIO.Browser;

export interface CustomAppCapabilities {
  appPackage?: string;
  appActivity?: string;
  [key: string]: any;
}

const options: RemoteOptions = {
  hostname: process.env.APPIUM_HOST || 'localhost',
  port: parseInt(process.env.APPIUM_PORT, 10) || 4723,
  logLevel: 'silent',
  capabilities:
    process.env.PLATFORM_NAME === 'Android'
      ? iosCapabilities
      : androidCapabilities,
};

export const getAppiumDriver = async (): Promise<AppiumDriver> => {
  try {
    return SuiteDataManager.get<AppiumDriver>(SuiteAppiumDriverKey);
  } catch (e) {
    const driver: AppiumDriver = await remote(options);
    SuiteDataManager.put(SuiteAppiumDriverKey, driver);
    return driver;
  }
};

/**
 * Get Appium driver with custom app package and activity
 * @param customCapabilities - Custom capabilities including appPackage and appActivity
 * @param cacheKey - Optional cache key to store multiple drivers (default: SuiteAppiumDriverKey)
 * @returns Promise<AppiumDriver>
 */
export const getCustomAppiumDriver = async (
  customCapabilities: CustomAppCapabilities,
  cacheKey: string = SuiteAppiumDriverKey,
): Promise<AppiumDriver> => {
  try {
    return SuiteDataManager.get<AppiumDriver>(cacheKey);
  } catch (e) {
    // Create base capabilities from environment (keep all env values)
    const baseCapabilities =
      process.env.PLATFORM_NAME === 'Android'
        ? { ...androidCapabilities }
        : { ...iosCapabilities };

    // Only override APP_PACKAGE and APP_ACTIVITY if provided
    const mergedCapabilities = {
      ...baseCapabilities,
      ...(customCapabilities.appPackage && {
        'appium:appPackage': customCapabilities.appPackage,
      }),
      ...(customCapabilities.appActivity && {
        'appium:appActivity': customCapabilities.appActivity,
      }),
      // Add other custom capabilities (excluding appPackage and appActivity)
      ...Object.fromEntries(
        Object.entries(customCapabilities)
          .filter(([key]) => !['appPackage', 'appActivity'].includes(key))
          .map(([key, value]) => [`appium:${key}`, value]),
      ),
    };

    const customOptions: RemoteOptions = {
      hostname: process.env.APPIUM_HOST || 'localhost',
      port: parseInt(process.env.APPIUM_PORT, 10) || 4723,
      logLevel: 'silent',
      capabilities: mergedCapabilities,
    };

    const driver: AppiumDriver = await remote(customOptions);
    SuiteDataManager.put(cacheKey, driver);
    return driver;
  }
};

export const terminateDriver = async () => {
  try {
    const driver: AppiumDriver =
      SuiteDataManager.get<AppiumDriver>(SuiteAppiumDriverKey);
    await driver.deleteSession();
  } catch (e) {}
};

/**
 * Terminate custom driver by cache key
 * @param cacheKey - Cache key of the driver to terminate
 */
export const terminateCustomDriver = async (
  cacheKey: string = SuiteAppiumDriverKey,
) => {
  try {
    const driver: AppiumDriver = SuiteDataManager.get<AppiumDriver>(cacheKey);
    await driver.deleteSession();
  } catch (e) {}
};
