import { BrowserRunTest } from '@libs/constants/global.const';
import { SpecDataManager } from '@libs/gauge/spec.data-manager';
import { SuiteDataManager } from '@libs/gauge/suite.data.manager';
import { getCurrentStepTitle } from '@libs/utils/gauge.util';
import {
  B<PERSON>er,
  BrowserContext,
  chromium,
  firefox,
  Locator,
  Page,
  webkit,
} from 'playwright';
import {
  IsHeadless,
  IsMobile,
  PinnedLocatorKey,
  SpecContextKey,
  SuiteBrowserKey,
  ViewportHeight,
  ViewportWidth,
} from './config.const';

export const getSuiteBrowser = async (): Promise<Browser> => {
  try {
    return SuiteDataManager.get<Browser>(SuiteBrowserKey);
  } catch (e) {
    const browser: Browser = await getBrowser(BrowserRunTest).launch({
      headless: IsHeadless,
    });
    SuiteDataManager.put(SuiteBrowserKey, browser);
    return browser;
  }
};

export const getSpecContext = async (): Promise<BrowserContext> => {
  try {
    return SpecDataManager.get<BrowserContext>(SpecContextKey);
  } catch (e) {
    const browser = await getSuiteBrowser();
    const ctx = await browser.newContext({
      hasTouch: true,
      isMobile: IsMobile,
      viewport: { width: ViewportWidth, height: ViewportHeight },
      ignoreHTTPSErrors: true,
    });
    SpecDataManager.put(SpecContextKey, ctx);
    return ctx;
  }
};

export const getSpecPage = async (index: number = 0): Promise<Page> => {
  const ctx = await getSpecContext();
  const pages = await addPage(ctx, index);
  return pages[index < 0 ? pages.length + index : index];
};

export const getNewestPage = async () => {
  const ctx = await getSpecContext();
  return ctx.pages()[ctx.pages().length - 1];
};

export const hasSpecPage = async (): Promise<boolean> => {
  return (
    SpecDataManager.has(SpecContextKey) &&
    (await getSpecContext()).pages().length > 0
  );
};

export const terminateSpecPage = async (): Promise<void> => {
  try {
    const browser: Browser = SuiteDataManager.get<Browser>(SuiteBrowserKey);
    await Promise.all(browser.contexts().map((c) => c.close()));
  } catch (e) {}
};

export const setPinnedLocator = (locator: Locator) => {
  SpecDataManager.put(PinnedLocatorKey, locator);
};

export const getPinnedLocator = (): Locator => {
  return SpecDataManager.get<Locator>(PinnedLocatorKey);
};

export const getTargetLocator = async (): Promise<Page | Locator> => {
  if (getCurrentStepTitle().startsWith('=>')) {
    return getPinnedLocator();
  }

  return await getSpecPage();
};

const getBrowser = (browserType: string) => {
  switch (browserType) {
    case 'chromium':
      return chromium;
    case 'firefox':
      return firefox;
    case 'webkit':
      return webkit;
    default:
      return null;
  }
};

const addPage = async (ctx: BrowserContext, index: number): Promise<Page[]> => {
  const pages = ctx.pages();
  const count = index < 0 ? -index : index + 1;
  if (pages.length < count) {
    for (let i = pages.length; i < count; i++) {
      await ctx.newPage();
    }
  }

  return ctx.pages();
};
