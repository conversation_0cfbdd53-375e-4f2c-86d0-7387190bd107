# Recaculate invoice on row has <name1>, verify invoice value <value>
### Input data
* Web: Open tab "Phiếu thu đã gửi" in on new collection period
* Web: On row that has "MiMi", click the checkbox in the first cell
* Web: Click button with "Tính toán lại"
* Web: Click button with "OK"
* Web: Wait for page stable
### Verify data 
* Web: Open tab "Bảng tính phí" in on new collection period
* Web: On row that has <name1>, doing step:
* Web: => Verify on column "Fees" has value <value>
* Web: Open tab "Xác nhận thanh toán" in on new collection period
* Wait "2" second
* Web: Verify the total amount on card is the same as sum of data on column "Tổng thu"