import { LogStartEnd } from '@libs/decorators/log.decorator';
import { getNewestPage } from '@libs/resources/playwright.resource';
import { Step } from 'gauge-ts';
import { Header } from './header.component';

export class HeaderSteps {
  @LogStartEnd()
  @Step(
    'Web: Click on your account icon in the header to open the account drop-down menu',
  )
  public async clickUserName() {
    const elem = await (await Header.getInstance()).accountInfoLocator();
    await elem.waitFor();
    await elem.click();
  }

  @LogStartEnd()
  @Step(
    'Web: Click on your language icon in the header to open the language drop-down menu',
  )
  public async clickLanguageChange() {
    const elem = await (await Header.getInstance()).languageLocator();
    await elem.waitFor();
    await elem.click();
  }

  @LogStartEnd()
  @Step('Web: Click on your chat icon in the header to open the chat system')
  public async clickChatIcon() {
    const elem = await (await Header.getInstance()).chatButton();
    await elem.click();
  }

  @LogStartEnd()
  @Step(
    'Web: Click on your logo image in the header of chat system that has been openned to go back to dashboard',
  )
  public async clickLogoImageInChatPage() {
    const page = await getNewestPage();
    const elem = page.locator('.logo_image');
    await elem.click();
  }

  @LogStartEnd()
  @Step(
    'Web: Click on profile switching button in the header to open profile list menu',
  )
  public async clickProfileSwitchingButton() {
    const elem = await (await Header.getInstance()).profileSwitchButton();
    await elem.click();
  }

  @LogStartEnd()
  @Step('Web: Click on notification button in the header to open list menu')
  public async clickNotificationButton() {
    const elem = await (await Header.getInstance()).notiButton();
    await elem.click();
  }
}
