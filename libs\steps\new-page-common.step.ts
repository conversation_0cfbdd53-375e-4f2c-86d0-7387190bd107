import { getNewestPage } from '@libs/resources/playwright.resource';
import { Step } from 'gauge-ts';
import { expect } from 'playwright/test';
import { WebCommonSteps } from './web-common.step';

export class NewPageCommonSteps {
  @Step([
    'Web: Verify title of new page is <title>',
    'Web: => Verify title of new page is <title>',
  ])
  public async verifyTitle(title: string) {
    const page = await getNewestPage();
    await expect(page).toHaveTitle(title);
  }

  @Step([
    'Web: Verify title of new page is started with text <text>',
    'Web: => Verify title of new page is started with text <text>',
  ])
  public async titleContains(title: string) {
    const page = await getNewestPage();
    await expect(page).toHaveTitle(new RegExp(`^${title}*`));
  }

  @Step([
    'Web: Verify URL of new page of current <domain> is <targetUrl>',
    'Web: => Verify url of new page of current <domain> is <targetUrl>',
  ])
  public async verifyUrl(domain: string, targetUrl: string) {
    const page = await getNewestPage();
    await expect(page).toHaveURL(
      `${WebCommonSteps.getDomain(domain)}/${targetUrl}`,
    );
  }

  @Step(['Web: Close newly openned page', 'Web: => Close newly openned page'])
  public async closePage() {
    const page = await getNewestPage();
    await page.close();
  }
}
