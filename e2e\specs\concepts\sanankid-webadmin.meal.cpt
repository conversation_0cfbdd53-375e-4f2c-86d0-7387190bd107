# Web: Enter <dish_name> into dish name field in add dish form
* Web: Input <dish_name> into field with locator "[id^='mat-input-']" index "0"

# Web: Right click on a dish item in the menu list
* Web: Right click on element "[id='0-0']"

# Web: Click icon [x] to close the add dish side panel
* Web: Click with "clear"

# Web: Enter <search_keyword> into the search bar on the dish list screen
* Web: Input <search_keyword> into field with locator "input.mat-form-field-autofill-control" index "0"

# Web: Click the apply block checkbox <checkbox_name> on the meal settings screen
* Web: Click element with locator "[formcontrolname='gradeIds']" and index "0"
* Web: Click element with text <checkbox_name> inside ".mat-option-text"
* Web: Click element with locator ".cdk-overlay-backdrop" and index "0"

# Web: Enter <time> into the time to eat field in add dish form
* Web: Input <time> into field with locator "[id^='mat-input-']" index "1"

# Web: Enter <cost> into the cost field in add dish form
* Web: Input <cost> into field with locator "[id^='mat-input-']" index "2"

# Web: Click the add button in the add dish form
* Web: Click element with locator ".mat-button-wrapper" and index "1"
