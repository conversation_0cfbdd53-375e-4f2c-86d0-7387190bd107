import { Step } from 'gauge-ts';
import { Finance } from '../finance.page';
import { AddNewCollectionPeriod } from './add-new-collection-period-popup.component';
import { TableBase } from '@libs/bases/web/table.base';

export class AddNewCollectionPeriodSteps {
  @Step('Web: Locate add new collection period, doing step:')
  public async pinPopup() {
    const elem = await (
      await Finance.getInstance()
    ).addNewCollectionPeriodPopup();
    AddNewCollectionPeriod.setLocator(elem);
  }
  
  @Step('Web: => Fill collection period name <name>')
  public async fill(name: string) {
    const elem = await (
      await AddNewCollectionPeriod.getInstance()
    ).collectionPeriodName();
    await elem.click();
    await elem.clear();
    await elem.fill(name);
  }

  @Step('Web: => Click on scope to open collection period scope')
  public async scopeCollectionPeriod() {
    const elem = await (
      await AddNewCollectionPeriod.getInstance()
    ).locateCollectionPeriodScope();
    await elem.click();
  }

  @Step('Web: => Click the check box on collection period popup')
  public async verifyColumnText() {
    const col = await AddNewCollectionPeriod.getInstance().checkbox();
    await col.click();
  }
}
