# SANAN Kid - Web Admin - Sidebar - Finance

tags: web,sanankid-web-admin,FQAC

## FQAC_000012

### Login page
* Login sanankid web with "0854124589" and "27041998", then select profile "Automation Test" of school "Trường Trường <PERSON>"

### Input data
* Web: Click on menu "Tài chính" on sidebar
* Web: Click on item "Thiết lập mức thu" on menu
* Web: Click button with "Thêm mới"
* Web: Click with "Thêm mới đối tượng áp dụng" exact
* Fill fee name "TestFee", fee "1000000" and unit "112" to add new fee
* Web: Click on "Toàn Trường" to open scope menu
* Web: Click on item to choose scope "Nhóm học sinh cụ thể"
* Wait "3" second
* Web: Click the search bar to find student
* Wait "500" milisecond
* Web: Keyboard press key "M"
* Web: Keyboard press key "i"
* Web: Keyboard press key "M"
* Web: Keyboard press key "i"
* Wait for "MiMi" appear
* Web: Click with "MiMi" exact
* Web: Keyboard press key "Escape"
* Web: Click button with "Thêm mới"
## Verify data
* Web: On row that has "MiMi", doing step:
* Web: => Verify on column "Đối tượng áp dụng" has "MiMi"
* Web: => Verify on column "Mức phí" has "1,000,000 VND"
* Web: => Verify on column "Đơn vị" has "112"
* Web: On row that has "TestFee", doing step:
* Web: => Click on edit button (button 2) on column "Tác vụ"
* Web: Click button with "Đồng ý" exact
* Wait "2" second