import { LogStartEnd } from '@libs/decorators/log.decorator';
import { 
  getCustomAppiumDriver, 
  terminateCustomDriver,
  CustomAppCapabilities 
} from '@libs/resources/appium.resource';
import { Step } from 'gauge-ts';

export class CustomMobileSteps {

  /**
   * Launch specific app with custom package and activity
   * Usage: * Mobile: Launch app with package "com.sanan.sanankids.manager.dev" and activity "com.sanan.sanankids.manager.MainActivity"
   */
  @LogStartEnd()
  @Step('Mobile: Launch app with package <appPackage> and activity <appActivity>')
  public async launchAppWithCustomPackageActivity(appPackage: string, appActivity: string) {
    const customCapabilities: CustomAppCapabilities = {
      appPackage: appPackage,
      appActivity: appActivity
    };

    const cacheKey = `custom_app_${appPackage.replace(/\./g, '_')}`;
    await getCustomAppiumDriver(customCapabilities, cacheKey);
  }

  /**
   * Launch manager app specifically
   * Usage: * Mobile: Launch manager app
   */
  @LogStartEnd()
  @Step('Mobile: Launch manager app')
  public async launchManagerApp() {
    const customCapabilities: CustomAppCapabilities = {
      appPackage: 'com.sanan.sanankids.manager.dev',
      appActivity: 'com.sanan.sanankids.manager.MainActivity'
    };

    await getCustomAppiumDriver(customCapabilities, 'manager_app');
  }

  /**
   * Launch parent app specifically  
   * Usage: * Mobile: Launch parent app
   */
  @LogStartEnd()
  @Step('Mobile: Launch parent app')
  public async launchParentApp() {
    const customCapabilities: CustomAppCapabilities = {
      appPackage: 'com.sanankids.parent.dev',
      appActivity: 'com.sanankids.parent.MainActivity'
    };

    await getCustomAppiumDriver(customCapabilities, 'parent_app');
  }

  /**
   * Launch app with additional capabilities
   * Usage: * Mobile: Launch app "manager" with no reset
   */
  @LogStartEnd()
  @Step('Mobile: Launch app <appType> with no reset')
  public async launchAppWithNoReset(appType: string) {
    let customCapabilities: CustomAppCapabilities;

    switch (appType.toLowerCase()) {
      case 'manager':
        customCapabilities = {
          appPackage: 'com.sanan.sanankids.manager.dev',
          appActivity: 'com.sanan.sanankids.manager.MainActivity',
          noReset: true,
          autoGrantPermissions: true
        };
        break;
      case 'parent':
        customCapabilities = {
          appPackage: 'com.sanankids.parent.dev',
          appActivity: 'com.sanankids.parent.MainActivity',
          noReset: true,
          autoGrantPermissions: true
        };
        break;
      default:
        throw new Error(`Unsupported app type: ${appType}`);
    }

    await getCustomAppiumDriver(customCapabilities, `${appType}_app_no_reset`);
  }

  /**
   * Terminate specific app driver
   * Usage: * Mobile: Terminate manager app
   */
  @LogStartEnd()
  @Step('Mobile: Terminate <appType> app')
  public async terminateApp(appType: string) {
    const cacheKey = `${appType.toLowerCase()}_app`;
    await terminateCustomDriver(cacheKey);
  }

  /**
   * Terminate custom app driver by package name
   * Usage: * Mobile: Terminate app with package "com.sanan.sanankids.manager.dev"
   */
  @LogStartEnd()
  @Step('Mobile: Terminate app with package <appPackage>')
  public async terminateAppByPackage(appPackage: string) {
    const cacheKey = `custom_app_${appPackage.replace(/\./g, '_')}`;
    await terminateCustomDriver(cacheKey);
  }

  /**
   * Switch between apps during test
   * Usage: * Mobile: Switch to app "manager"
   */
  @LogStartEnd()
  @Step('Mobile: Switch to app <appType>')
  public async switchToApp(appType: string) {
    let customCapabilities: CustomAppCapabilities;

    switch (appType.toLowerCase()) {
      case 'manager':
        customCapabilities = {
          appPackage: 'com.sanan.sanankids.manager.dev',
          appActivity: 'com.sanan.sanankids.manager.MainActivity'
        };
        break;
      case 'parent':
        customCapabilities = {
          appPackage: 'com.sanankids.parent.dev',
          appActivity: 'com.sanankids.parent.MainActivity'
        };
        break;
      default:
        throw new Error(`Unsupported app type: ${appType}`);
    }

    const cacheKey = `${appType}_app`;
    await getCustomAppiumDriver(customCapabilities, cacheKey);
  }
}
