import { LogStartEnd } from '@libs/decorators/log.decorator';
import { SuiteDataManager } from '@libs/gauge/suite.data.manager';
import {
  AppiumDriver,
  CustomAppCapabilities,
  getCustomAppiumDriver,
  terminateCustomDriver,
} from '@libs/resources/appium.resource';
import { Step } from 'gauge-ts';

export class CustomMobileSteps {

  private static getDriverFromCache(cacheKey: string): AppiumDriver {
    try {
      return SuiteDataManager.get<AppiumDriver>(cacheKey);
    } catch (e) {
      throw new Error(
        `Driver with cache key '${cacheKey}' not found. Make sure to launch the app first.`,
      );
    }
  }
  
  @LogStartEnd()
  @Step(
    'Mobile: Launch app with package <appPackage> and activity <appActivity>',
  )
  public async launchAppWithCustomPackageActivity(
    appPackage: string,
    appActivity: string,
  ) {
    const customCapabilities: CustomAppCapabilities = {
      appPackage: appPackage,
      appActivity: appActivity,
    };

    const cacheKey = `custom_app_${appPackage.replace(/\./g, '_')}`;
    const driver = await getCustomAppiumDriver(customCapabilities, cacheKey);

    await driver.pause(3000);
  }

  @LogStartEnd()
  @Step('Mobile: Launch manager app')
  public async launchManagerApp() {
    const customCapabilities: CustomAppCapabilities = {
      appPackage: 'com.sanan.sanankids.manager.dev',
      appActivity: 'com.sanan.sanankids.manager.MainActivity',
    };

    const driver = await getCustomAppiumDriver(
      customCapabilities,
      'manager_app',
    );

    await driver.pause(3000);
  }

  @LogStartEnd()
  @Step('Mobile: Launch parent app')
  public async launchParentApp() {
    const customCapabilities: CustomAppCapabilities = {
      appPackage: 'com.sanankids.parent.dev',
      appActivity: 'com.sanankids.parent.MainActivity',
    };

    const driver = await getCustomAppiumDriver(
      customCapabilities,
      'parent_app',
    );

    await driver.pause(3000);
  }

  @LogStartEnd()
  @Step('Mobile: Launch teacher app')
  public async launchTeacherApp() {
    const customCapabilities: CustomAppCapabilities = {
      appPackage: 'com.sanankids.teacher.dev',
      appActivity: 'com.sanankids.teacher.MainActivity',
    };

    const driver = await getCustomAppiumDriver(
      customCapabilities,
      'teacher_app',
    );

    await driver.pause(3000);
  }

  @LogStartEnd()
  @Step('Mobile: Launch app <appType> with no reset')
  public async launchAppWithNoReset(appType: string) {
    let customCapabilities: CustomAppCapabilities;

    switch (appType.toLowerCase()) {
      case 'manager':
        customCapabilities = {
          appPackage: 'com.sanan.sanankids.manager.dev',
          appActivity: 'com.sanan.sanankids.manager.MainActivity',
          noReset: true,
          autoGrantPermissions: true,
        };
        break;
      case 'parent':
        customCapabilities = {
          appPackage: 'com.sanankids.parent.dev',
          appActivity: 'com.sanankids.parent.MainActivity',
          noReset: true,
          autoGrantPermissions: true,
        };
        break;
      default:
        throw new Error(`Unsupported app type: ${appType}`);
    }

    await getCustomAppiumDriver(customCapabilities, `${appType}_app_no_reset`);
  }

  @LogStartEnd()
  @Step('Mobile: Terminate <appType> app')
  public async terminateApp(appType: string) {
    const cacheKey = `${appType.toLowerCase()}_app`;
    await terminateCustomDriver(cacheKey);
  }

  @LogStartEnd()
  @Step('Mobile: Terminate app with package <appPackage>')
  public async terminateAppByPackage(appPackage: string) {
    const cacheKey = `custom_app_${appPackage.replace(/\./g, '_')}`;
    await terminateCustomDriver(cacheKey);
  }

  @LogStartEnd()
  @Step('Mobile: Switch to app <appType>')
  public async switchToApp(appType: string) {
    let customCapabilities: CustomAppCapabilities;

    switch (appType.toLowerCase()) {
      case 'manager':
        customCapabilities = {
          appPackage: 'com.sanan.sanankids.manager.dev',
          appActivity: 'com.sanan.sanankids.manager.MainActivity',
        };
        break;
      case 'parent':
        customCapabilities = {
          appPackage: 'com.sanankids.parent.dev',
          appActivity: 'com.sanankids.parent.MainActivity',
        };
        break;
      default:
        throw new Error(`Unsupported app type: ${appType}`);
    }

    const cacheKey = `${appType}_app`;
    await getCustomAppiumDriver(customCapabilities, cacheKey);
  }

  @LogStartEnd()
  @Step('Mobile: Using <appType> app click with <text>')
  public async clickWithSpecificApp(appType: string, text: string) {
    const cacheKey = `${appType.toLowerCase()}_app`;
    const driver = CustomMobileSteps.getDriverFromCache(cacheKey);

    const locator = await driver.$(`//*[contains(@text, '${text}')]`);
    await locator.waitForDisplayed();
    await locator.click();
  }

  @LogStartEnd()
  @Step('Mobile: Using app <appPackage> click with <text>')
  public async clickWithCustomApp(appPackage: string, text: string) {
    const cacheKey = `custom_app_${appPackage.replace(/\./g, '_')}`;
    const driver = CustomMobileSteps.getDriverFromCache(cacheKey);

    const locator = await driver.$(`//*[contains(@text, '${text}')]`);
    await locator.waitForDisplayed();
    await locator.click();
  }

  @LogStartEnd()
  @Step('Mobile: Using <appType> app verify exact <text> is visible')
  public async verifyTextWithSpecificApp(appType: string, text: string) {
    const cacheKey = `${appType.toLowerCase()}_app`;
    const driver = CustomMobileSteps.getDriverFromCache(cacheKey);

    const locator = await driver.$(`//*[contains(@text, '${text}')]`);
    await locator.waitForDisplayed();
  }
}
