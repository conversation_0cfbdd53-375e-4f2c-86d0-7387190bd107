import { LogStartEnd } from '@libs/decorators/log.decorator';
import { SuiteDataManager } from '@libs/gauge/suite.data.manager';
import {
  AppiumDriver,
  CustomAppCapabilities,
  getCustomAppiumDriver,
  terminateCustomDriver,
} from '@libs/resources/appium.resource';
import { Step } from 'gauge-ts';

export class CustomMobileSteps {
  /**
   * Helper method to get driver from cache key
   */
  private static getDriverFromCache(cacheKey: string): AppiumDriver {
    try {
      return SuiteDataManager.get<AppiumDriver>(cacheKey);
    } catch (e) {
      throw new Error(
        `Driver with cache key '${cacheKey}' not found. Make sure to launch the app first.`,
      );
    }
  }
  /**
   * Launch specific app with custom package and activity
   * Usage: * Mobile: Launch app with package "com.sanan.sanankids.manager.dev" and activity "com.sanan.sanankids.manager.MainActivity"
   */
  @LogStartEnd()
  @Step(
    'Mobile: Launch app with package <appPackage> and activity <appActivity>',
  )
  public async launchAppWithCustomPackageActivity(
    appPackage: string,
    appActivity: string,
  ) {
    const customCapabilities: CustomAppCapabilities = {
      appPackage: appPackage,
      appActivity: appActivity,
      autoGrantPermissions: true,
      autoAcceptAlerts: true,
    };

    const cacheKey = `custom_app_${appPackage.replace(/\./g, '_')}`;
    const driver = await getCustomAppiumDriver(customCapabilities, cacheKey);

    // Ensure app is launched and ready
    await driver.pause(2000); // Wait for app to fully load

    // Try to activate the app if it's not in foreground
    try {
      await driver.activateApp(appPackage);
    } catch (e) {
      // If activateApp fails, the app might already be active
      console.log(`App ${appPackage} might already be active`);
    }
  }

  /**
   * Launch manager app specifically
   * Usage: * Mobile: Launch manager app
   */
  @LogStartEnd()
  @Step('Mobile: Launch manager app')
  public async launchManagerApp() {
    const appPackage = 'com.sanan.sanankids.manager.dev';
    const customCapabilities: CustomAppCapabilities = {
      appPackage: appPackage,
      appActivity: 'com.sanan.sanankids.manager.MainActivity',
      autoGrantPermissions: true,
      autoAcceptAlerts: true,
    };

    const driver = await getCustomAppiumDriver(
      customCapabilities,
      'manager_app',
    );

    // Ensure app is launched and ready
    await driver.pause(2000);

    try {
      await driver.activateApp(appPackage);
    } catch (e) {
      console.log(`Manager app might already be active`);
    }
  }

  /**
   * Launch parent app specifically
   * Usage: * Mobile: Launch parent app
   */
  @LogStartEnd()
  @Step('Mobile: Launch parent app')
  public async launchParentApp() {
    const appPackage = 'com.sanankids.parent.dev';
    const customCapabilities: CustomAppCapabilities = {
      appPackage: appPackage,
      appActivity: 'com.sanankids.parent.MainActivity',
      autoGrantPermissions: true,
      autoAcceptAlerts: true,
    };

    const driver = await getCustomAppiumDriver(
      customCapabilities,
      'parent_app',
    );

    // Ensure app is launched and ready
    await driver.pause(2000);

    try {
      await driver.activateApp(appPackage);
    } catch (e) {
      console.log(`Parent app might already be active`);
    }
  }

  /**
   * Launch app with additional capabilities
   * Usage: * Mobile: Launch app "manager" with no reset
   */
  @LogStartEnd()
  @Step('Mobile: Launch app <appType> with no reset')
  public async launchAppWithNoReset(appType: string) {
    let customCapabilities: CustomAppCapabilities;

    switch (appType.toLowerCase()) {
      case 'manager':
        customCapabilities = {
          appPackage: 'com.sanan.sanankids.manager.dev',
          appActivity: 'com.sanan.sanankids.manager.MainActivity',
          noReset: true,
          autoGrantPermissions: true,
        };
        break;
      case 'parent':
        customCapabilities = {
          appPackage: 'com.sanankids.parent.dev',
          appActivity: 'com.sanankids.parent.MainActivity',
          noReset: true,
          autoGrantPermissions: true,
        };
        break;
      default:
        throw new Error(`Unsupported app type: ${appType}`);
    }

    await getCustomAppiumDriver(customCapabilities, `${appType}_app_no_reset`);
  }

  /**
   * Terminate specific app driver
   * Usage: * Mobile: Terminate manager app
   */
  @LogStartEnd()
  @Step('Mobile: Terminate <appType> app')
  public async terminateApp(appType: string) {
    const cacheKey = `${appType.toLowerCase()}_app`;
    await terminateCustomDriver(cacheKey);
  }

  /**
   * Terminate custom app driver by package name
   * Usage: * Mobile: Terminate app with package "com.sanan.sanankids.manager.dev"
   */
  @LogStartEnd()
  @Step('Mobile: Terminate app with package <appPackage>')
  public async terminateAppByPackage(appPackage: string) {
    const cacheKey = `custom_app_${appPackage.replace(/\./g, '_')}`;
    await terminateCustomDriver(cacheKey);
  }

  /**
   * Switch between apps during test
   * Usage: * Mobile: Switch to app "manager"
   */
  @LogStartEnd()
  @Step('Mobile: Switch to app <appType>')
  public async switchToApp(appType: string) {
    let customCapabilities: CustomAppCapabilities;

    switch (appType.toLowerCase()) {
      case 'manager':
        customCapabilities = {
          appPackage: 'com.sanan.sanankids.manager.dev',
          appActivity: 'com.sanan.sanankids.manager.MainActivity',
        };
        break;
      case 'parent':
        customCapabilities = {
          appPackage: 'com.sanankids.parent.dev',
          appActivity: 'com.sanankids.parent.MainActivity',
        };
        break;
      default:
        throw new Error(`Unsupported app type: ${appType}`);
    }

    const cacheKey = `${appType}_app`;
    await getCustomAppiumDriver(customCapabilities, cacheKey);
  }

  /**
   * Click element using specific app driver
   * Usage: * Mobile: Using "manager" app click with "Login"
   */
  @LogStartEnd()
  @Step('Mobile: Using <appType> app click with <text>')
  public async clickWithSpecificApp(appType: string, text: string) {
    const cacheKey = `${appType.toLowerCase()}_app`;
    const driver = CustomMobileSteps.getDriverFromCache(cacheKey);

    const locator = await driver.$(`//*[contains(@text, '${text}')]`);
    await locator.waitForDisplayed();
    await locator.click();
  }

  /**
   * Click element using custom app driver by package
   * Usage: * Mobile: Using app "com.sanankids.teacher.dev" click with "Login"
   */
  @LogStartEnd()
  @Step('Mobile: Using app <appPackage> click with <text>')
  public async clickWithCustomApp(appPackage: string, text: string) {
    const cacheKey = `custom_app_${appPackage.replace(/\./g, '_')}`;
    const driver = CustomMobileSteps.getDriverFromCache(cacheKey);

    const locator = await driver.$(`//*[contains(@text, '${text}')]`);
    await locator.waitForDisplayed();
    await locator.click();
  }

  /**
   * Verify text using specific app driver
   * Usage: * Mobile: Using "manager" app verify exact "Welcome" is visible
   */
  @LogStartEnd()
  @Step('Mobile: Using <appType> app verify exact <text> is visible')
  public async verifyTextWithSpecificApp(appType: string, text: string) {
    const cacheKey = `${appType.toLowerCase()}_app`;
    const driver = CustomMobileSteps.getDriverFromCache(cacheKey);

    const locator = await driver.$(`//*[contains(@text, '${text}')]`);
    await locator.waitForDisplayed();
  }
}
