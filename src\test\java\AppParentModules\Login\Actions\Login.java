package AppParentModules.Login.Actions;

import AppParentModules.Login.Locators.LoginLocators;
import Base.PageObjectBase;
import io.appium.java_client.MobileDriver;
import io.appium.java_client.MobileElement;
import org.openqa.selenium.By;

public class Login extends PageObjectBase {
    public final String PHONE_NUMBER = "0866113736";
    public final String PASSWORD = "anhduc200510";

    public Login(MobileDriver<MobileElement> appiumDriver) {
        super(appiumDriver);
    }

    public void doLogin() {
        formInputByLocator(LoginLocators.textboxPhoneNumber, PHONE_NUMBER);
        clickOnElement(LoginLocators.buttonContinues, "button continue");
        formInputByLocator(LoginLocators.textboxPassword, PASSWORD);
        clickOnElement(LoginLocators.buttonLogin, "button login");
        waitElementIsDisplay(LoginLocators.profileList, MAX_WAIT_TIME);
        CaptureScreenShot("Login");
    }

    public void clickChooseProfile(By profile, String profileName) {
        clickOnElement(profile, "student name: \"" + profileName + "\"");
    }

}
