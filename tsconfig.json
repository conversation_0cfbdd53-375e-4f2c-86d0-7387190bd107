{
  "compilerOptions": {
    /* Basic Options */
    "target": "es6",
    /* Specify ECMAScript target version: 'ES3' (default), 'ES5', 'ES2015', 'ES2016', 'ES2017', or 'ESNEXT'. */
    "module": "commonjs",
    /* Specify module code generation: 'none', commonjs', 'amd', 'system', 'umd', 'es2015', or 'ESNext'. */
    "lib": [
      "es2016",
      "dom"
    ] /* Specify library files to be included in the compilation:  */,
    /* Module Resolution Options */
    "moduleResolution": "node",
    /* Experimental Options */
    "experimentalDecorators": true /* Enables experimental support for ES7 decorators. */,
    "emitDecoratorMetadata": true /* Enables experimental support for emitting type metadata for decorators. */,
    "baseUrl": "./" /* Base directory to resolve non-absolute module names. */,
    "paths": {
      "@libs/*": ["libs/*"],
      "tests/*": ["tests/*"]
    }
  },
  "exclude": ["node_modules"]
}
