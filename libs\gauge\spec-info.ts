import { ExecutionContext } from 'gauge-ts';
import { basename, dirname, join } from 'path';
import { existsSync } from 'fs';
import {
  LogFileExtension,
  ScreenshotOptionFileName,
  SpecDirectory,
  SpecFileExtension,
} from '@libs/constants/global.const';
import { createUniqueID } from '@libs/utils/uuid.util';

export class SpecInfo {
  public readonly specFilePath: string;
  public readonly tags: string[];
  public readonly specID: string;
  public readonly logFilePath: string;
  private screenshotOptionFilePath: string;

  private constructor(specFilePath: string, tags: string[], specID: string) {
    const specdir = dirname(specFilePath);
    this.specFilePath = specFilePath;
    this.tags = tags;
    this.specID = specID;
    this.screenshotOptionFilePath = join(specdir, ScreenshotOptionFileName);
    this.logFilePath = SpecInfo._getLogFilePath(specFilePath);
  }

  private static _getLogFilePath(specFilePath: string): string {
    const pathelem = specFilePath.split('\\');
    const idx = pathelem.findIndex((elem) => elem === SpecDirectory);
    if (idx === -1) {
      throw new Error(`invalid specFilePath: ${specFilePath}`);
    }
    pathelem[pathelem.length - 1] = pathelem[pathelem.length - 1].replace(
      SpecFileExtension,
      LogFileExtension,
    );

    return pathelem.slice(idx + 1).join('/');
  }

  public static fromContext(ctx: ExecutionContext): SpecInfo {
    const currentSpec = ctx.getCurrentSpec();
    if (!currentSpec) {
      throw new Error(`currentSpec is null`);
    }
    const specName = currentSpec.getFileName();
    if (!specName) {
      throw new Error(`specName is null`);
    }

    return new SpecInfo(
      specName,
      currentSpec.getTags() ?? [],
      `${createUniqueID()}`,
    );
  }

  public findScreenshotOptionFile(): string | null {
    let optionFile: string | null = this.screenshotOptionFilePath;
    if (!optionFile) {
      return null;
    }

    while (!existsSync(optionFile)) {
      const dir = dirname(optionFile);
      const base = basename(dir);
      if (base === 'specs' || base === '' || dir === '/') {
        return null;
      }
      const parent = dirname(dir);
      optionFile = join(parent, ScreenshotOptionFileName);
    }

    this.screenshotOptionFilePath = optionFile;
    return optionFile;
  }
}
