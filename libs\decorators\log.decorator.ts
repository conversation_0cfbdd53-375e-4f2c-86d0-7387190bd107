import Logger from '@libs/log/logger';

export function LogStartEnd(logArgIndexes: number[] = []) {
  return (
    _target: object,
    propertyKey: string,
    descriptor: PropertyDescriptor,
  ) => {
    const originalMethod = descriptor.value;
    descriptor.value = async function (...args: any) {
      Logger.write(`Start: ${propertyKey}`);
      if (logArgIndexes.length > 0) {
        const logArgs = logArgIndexes
          .map((idx) => `[${idx}]${_toString(args[idx])}`)
          .join(', ');
        Logger.write(`Args: ${logArgs}`);
      }
      const result = await originalMethod.apply(this, args);
      Logger.write(`End: ${propertyKey}`);
      if (typeof result !== 'undefined') {
        Logger.write(`Result: ${_toString(result)}`);
      }

      return result;
    };
  };
}

const _toString = (value: any): string => {
  if (typeof value === 'undefined') return 'undefined';
  if (value === null) return 'null';
  if (typeof value === 'object') return JSON.stringify(value);
  return String(value);
};
