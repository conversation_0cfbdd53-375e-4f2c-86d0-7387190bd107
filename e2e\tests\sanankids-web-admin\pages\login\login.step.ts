import { SananKidsWebAdmin } from '@libs/constants/config.const';
import { LogStartEnd } from '@libs/decorators/log.decorator';
import { getSpecPage } from '@libs/resources/playwright.resource';
import { waitForAnimation } from '@libs/utils/wait.util';
import { Step } from 'gauge-ts';
import { LoginPage } from './login.page';

export class LoginPageSteps {
  @LogStartEnd()
  @Step('Web: Open sanankid web admin login page')
  public async openLogInPage() {
    const page = await getSpecPage();
    await page.goto(SananKidsWebAdmin);
    await waitForAnimation(page);
  }

  @LogStartEnd()
  @Step('Web: Fill username <username> and <password> to login')
  public async loginWithAccount(username: string, password: string) {
    const usernameInput = await (await LoginPage.getInstance()).usernameInput();
    const passwordInput = await (await LoginPage.getInstance()).passwordInput();
    await usernameInput.fill(username);
    await passwordInput.fill(password);
  }

  @LogStartEnd()
  @Step('Web: Click button login to sanankid web admin')
  public async loginWebAdmin() {
    const loginButton = await (await LoginPage.getInstance()).loginButton();
    await loginButton.click();
  }
}
