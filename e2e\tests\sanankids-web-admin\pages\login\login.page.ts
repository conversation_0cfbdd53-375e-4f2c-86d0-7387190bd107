import { PageBase } from '@libs/bases/web/page.base';
import { LocatorGetter } from '@libs/decorators/locator.decorator';
import { Page } from 'playwright';

export class LoginPage extends PageBase {
  public constructor(page: Page) {
    super(page);
  }

  @LocatorGetter()
  public async usernameInput() {
    return this.getElementByCssSelector('#username');
  }

  @LocatorGetter()
  public async passwordInput() {
    return this.getElementByCssSelector('#password');
  }

  @LocatorGetter()
  public async loginButton() {
    return this.getElementByCssSelector('#submit-btn');
  }
}
