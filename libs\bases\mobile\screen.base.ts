import { AppiumDriver, getAppiumDriver } from '@libs/resources/appium.resource';
import { MobileResource } from '@libs/resources/mobile.resource';

export abstract class ScreenBase extends MobileResource {
  protected constructor(public driver: AppiumDriver) {
    super(driver);
  }

  public static async getInstance<T extends MobileResource>(
    this: new (driver: AppiumDriver) => T,
  ): Promise<T> {
    return new this(await getAppiumDriver());
  }
}
