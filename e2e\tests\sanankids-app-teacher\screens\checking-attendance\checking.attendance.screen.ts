import { ScreenBase } from '@libs/bases/mobile/screen.base';

export class CheckingAttendanceScreen extends ScreenBase {
  constructor(public driver: WebdriverIO.Browser) {
    super(driver);
  }

  public rollBackAttendance() {
    return this.getElementByXpath('view.ViewGroup[@resource-id="attendance.arrived0"]');
  }

  public attendanceButton() {
    return this.getElementByXpath('view.ViewGroup[@resource-id="attendance"]');
  }

  public messageAttendanceSucess() {
    return this.getElementByXpath('widget.Toast[@text="Điểm danh thành công!"]');
  }

  public captureCheckinAttendance() {
    return this.getElementByXpath('view.ViewGroup[@resource-id="attendance.capture_checkin_attendance0"]');
  }

  public buttonCapture() {
    return this.driver.$('//com.horcrux.svg.g');
  }
}