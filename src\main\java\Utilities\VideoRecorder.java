package Utilities;

import io.appium.java_client.MobileDriver;
import io.appium.java_client.MobileElement;
import io.appium.java_client.screenrecording.CanRecordScreen;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;

public class VideoRecorder {
    private static final Logger logger = LoggerFactory.getLogger(VideoRecorder.class);
    public static final String PROJECT_PATH = System.getProperty("user.dir");
    private static final String SCREEN_RECORDING_PATH = PROJECT_PATH + File.separator + "screen-recordings";
    private static SimpleDateFormat simpleDateFormat = new SimpleDateFormat("dd-MM-yyyy HH-mm-ss");

    public static void startRecording(MobileDriver<MobileElement> driver) {
        ((CanRecordScreen) driver).startRecordingScreen();
    }

    public static void stopRecording(String methodName, MobileDriver<MobileElement> driver) {
        String recordedVideoFile = ((CanRecordScreen) driver).stopRecordingScreen();
        String pathToWriteVideoFile = getScreenRecordingsPath() + File.separator + methodName + simpleDateFormat.format(new Date()) + ".mp4";
        writeToOutputStream(pathToWriteVideoFile, recordedVideoFile);
    }

    public static void writeToOutputStream(String filePathToWrite, String recordedVideoFile) {
        try (FileOutputStream outputStream = new FileOutputStream(filePathToWrite)) {
            outputStream.write(Base64.getDecoder().decode(recordedVideoFile));
        } catch (IOException e) {
            logger.error("Error writing to output stream: {}", e.getMessage(), e);
        }
    }

    public static String getScreenRecordingsPath() {
        File screenRecordingsDir = new File(SCREEN_RECORDING_PATH);
        if (!screenRecordingsDir.exists()) {
            boolean created = screenRecordingsDir.mkdirs();
            if (!created) {
                logger.error("Failed to create directory: {}", SCREEN_RECORDING_PATH);
            }
        }
        return SCREEN_RECORDING_PATH;
    }
}
