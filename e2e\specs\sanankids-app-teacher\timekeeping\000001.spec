# SANAN Kid - App teacher

tags: mobile,sanankid-app-teacher,android

table: e2e\specs\sanankids-app-teacher\timekeeping\test-data\data.csv

## 000001
### Login
* Login sanankid app teacher with <username> and <password>
* Mobile: Click with <class>
* Mobile: Click button allow permission controler
### Timekeeping
* Mobile: Click button timekeeping on today screen
* Mobile: Verify exact "Thêm ảnh chấm công đến" is visible
* Mobile: Click with "Thêm ảnh chấm công đến"
* Mobile: Click sys button on timekeeping screen
* Mobile: Capture screenshot
* Mobile: Click sys button on timekeeping screen
* Mobile: Verify exact "Thêm ảnh chấm công đến" is visible
* Mobile: Click with "Thêm ảnh chấm công đến"
* Mobile: Click sys button on timekeeping screen
* Mobile: Click with "Xác nhận"
* Mobile: Capture screenshot
* Mobile: Verify exact "Đã chấm công thành công. <PERSON><PERSON><PERSON> bạn một ngày mới tốt lành!!!" is visible
* Mobile: Capture screenshot
* Mobile: Click with "Đóng"
### Timekeeping when leaving
* Mobile: Verify exact "Thêm ảnh chấm công về" is visible
* Mobile: Click with "Thêm ảnh chấm công về"
* Mobile: Click sys button on timekeeping screen
* Mobile: Capture screenshot
* Mobile: Click sys button on timekeeping screen
* Mobile: Verify exact "Thêm ảnh chấm công về" is visible
* Mobile: Click with "Thêm ảnh chấm công về"
* Mobile: Click sys button on timekeeping screen
* Mobile: Click with "Xác nhận"
* Mobile: Capture screenshot
* Mobile: Verify exact "Đã chấm công thành công. Chúc bạn buổi tối vui vẻ sau một ngày làm việc nỗ lực. Hẹn gặp lại vào ngày mai!!!" is visible
* Mobile: Capture screenshot
* Mobile: Click with "Đóng"
### Verify on web-admin
* Web: Open sanankid web admin login page
* Web: Fill username <username> and <password> to login
* Web: Click button login to sanankid web admin
* Web: Wait for page stable
* Web: Click with <schoolName>
* Web: Click with <profileName>
* Web: Click on menu "Nhân sự" on sidebar
* Web: Click on item "Chấm công nhân viên" on menu
* Web: Wait for page stable
* Web: On row that has <employeeName>, doing step:
* Web: => Verify on column "Trạng thái" has "Đã về"
* Web: Capture screenshot
* Web: Wait for page stable
* Web: On row that has <employeeName>, doing step:
* Web: => Click on edit button (button undo 1) on column "colName"
* Web: Click with "Đồng ý"
* Web: Wait for page stable
* Web: => Click on edit button (button undo 2) on column "colName"
* Web: Click with "Đồng ý"
* Web: Wait for page stable
* Web: => Verify on column "Trạng thái" has "Chưa đến"
