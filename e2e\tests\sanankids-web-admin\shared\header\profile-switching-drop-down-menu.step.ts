import { LogStartEnd } from '@libs/decorators/log.decorator';
import { Step } from 'gauge-ts';
import { Header } from './header.component';
import { ProfileSwitchingDropDownMenu } from './profile-switching-drop-down-menu.component';

export class ProfileSwitchingDropDownMenuSteps {
  @LogStartEnd()
  @Step('Web: On profile switching drop-down menu in the header:')
  public async pinProfileDropDownMenu() {
    const elem = await (await Header.getInstance()).profileSwitchDropDown();
    ProfileSwitchingDropDownMenu.setLocator(elem);
  }

  @LogStartEnd()
  @Step('Web: => Select profile <profileName> on profile list')
  public async clickProfileByName(profileName: string) {
    const elem = await (
      await ProfileSwitchingDropDownMenu.getInstance()
    ).getElementByText(profileName);
    await elem.click();
  }
}
