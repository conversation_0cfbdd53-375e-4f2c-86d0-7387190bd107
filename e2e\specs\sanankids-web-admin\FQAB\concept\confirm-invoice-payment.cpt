# Confirm invoice payment on row has <name1>, <name2>
### Input data
* Web: Open tab "Xác nhận thanh toán" in on new collection period
* Web: On row that has <name1>, click the checkbox in the first cell
* Web: On row that has <name2>, click the checkbox in the first cell
* Web: Click button with "Xác nhận thanh toán"
* Web: Click button with "Xác nhận thanh toán"
### Verify data
* Web: Wait for page stable
* Web: On row that has <name1>, doing step:
* Web: => Verify on column "Họ và tên" has "Đã thanh toán"
* Web: On row that has <name2>, doing step:
* Web: => Verify on column "Họ và tên" has "Đã thanh toán"
* Web: Verify the paid amount on card is the same as sum of data on column "Đã đóng"
* Web: Verify the unpaid amount on card is the same as sum of data on column "Còn thiếu"