import { Step } from 'gauge-ts';
import { Finance } from '../finance.page';
import { AddNewFee } from './add-new-fee-popup.component';

export class AddNewFeeSteps {
  @Step('Web: Locate add new fee, doing step:')
  public async pinPopup() {
    const elem = await (await Finance.getInstance()).addNewFeePopup();
    AddNewFee.setLocator(elem);
  }

  @Step('Web: => Click on submit button')
  public async scopeCollectionPeriod() {
    const elem = await AddNewFee.getInstance().submitButton();
    await elem.click();
  }
}
