import { TableBase } from '@libs/bases/web/table.base';
import { SananKidsWebAdmin } from '@libs/constants/config.const';
import { Domain } from '@libs/constants/global.const';
import {
  getSpecPage,
  getTargetLocator,
} from '@libs/resources/playwright.resource';
import { waitForAnimation, waitForText } from '@libs/utils/wait.util';
import { expect } from '@playwright/test';
import { Gauge, Step } from 'gauge-ts';

export class WebCommonSteps {
  @Step('Web: Wait for page stable')
  public async waitForAnimation() {
    const page = await getSpecPage();
    await waitForAnimation(page);
  }

  @Step([
    'Web: Verify exact <text> is visible',
    'Web: => Verify exact <text> is visible',
  ])
  public async assertTextToBeVisible(text: string) {
    const page = await getTargetLocator();
    const locator = page.getByText(text, { exact: true }).first();
    await expect(locator).toBeVisible();
  }

  @Step('Web: Verify checkbox with <name> is <ON_or_OFF>')
  public async assertCheckboxToBeChecked(
    name: string,
    ON_or_OFF: 'ON' | 'OFF',
  ) {
    const page = await getSpecPage();
    const checkbox = page.getByRole('checkbox', { name }).first();
    if (ON_or_OFF === 'ON') {
      await expect(checkbox).toBeChecked();
    } else {
      await expect(checkbox).not.toBeChecked();
    }
  }

  @Step('Web: Check radio with <name> is <ON_or_OFF>')
  public async assertRadioButtonToBeChecked(
    name: string,
    ON_or_OFF: 'ON' | 'OFF',
  ) {
    const page = await getSpecPage();
    const radio = page.getByRole('radio', { name }).first();
    if (ON_or_OFF === 'ON') {
      await expect(radio).toBeChecked();
    } else {
      await expect(radio).not.toBeChecked();
    }
  }

  @Step('Web: Click with <text>')
  public async clickText(text: string) {
    const page = await getSpecPage();
    await page.getByText(text).click();
  }

  @Step('Web: Click with <text> exact')
  public async clickTextExact(text: string) {
    const page = await getSpecPage();
    await page.getByText(text, { exact: true }).first().click();
  }

  @Step('Web: Click button with <name>')
  public async clickButton(name: string) {
    const page = await getSpecPage();
    await page.getByRole('button', { name }).first().click();
  }

  @Step('Web: Click button with <name> exact')
  public async clickButtonExact(name: string) {
    const page = await getSpecPage();
    await page.getByRole('button', { name, exact: true }).first().click();
  }

  @Step('Web: Reload page')
  public async reloadPage() {
    const page = await getSpecPage();
    await page.reload();
    await waitForAnimation(page);
  }

  @Step('Web: Verify URL of current <domain> page is <targetUrl>')
  public async verifyUrl(domain: string, targetUrl: string) {
    const page = await getSpecPage();
    await waitForAnimation(page);
    await expect(page).toHaveURL(
      `${await WebCommonSteps.getDomain(domain)}/${targetUrl}`,
    );
  }

  @Step('Web: Verify web title is <title>')
  public async verifyTitle(title: string) {
    const page = await getSpecPage();
    await waitForAnimation(page);
    await expect(page).toHaveTitle(title);
  }

  @Step('Web: Keyboard press key <key>')
  public async pressKeysEnter(key: string) {
    const page = await getSpecPage();
    await page.keyboard.press(key);
  }

  @Step('Web: Input field by placeholder <placeholder> with <text>')
  public async inputTextByPlaceholderField(placeholder: string, text: string) {
    const page = await getSpecPage();
    const element = page.getByPlaceholder(placeholder).first();
    await element.click();
    await element.clear();
    await element.fill(text);
  }

  @Step('Web: Input field by id <id> with <text>')
  public async inputTextById(id: string, text: string) {
    const page = await getSpecPage();
    const element = page.locator(id);
    await element.click();
    await element.clear();
    await element.fill(text);
  }

  @Step([
    'Web: Check exact <text> is displayed on page',
    'Web: => Verify exact <text> is displayed on page',
  ])
  public async verifyTextOnPage(text: string) {
    const page = await getTargetLocator();
    const locator = page.getByText(text, { exact: true }).first();
    await expect(locator).toContainText(text);
  }

  public static getDomain(domain: string) {
    switch (domain) {
      case Domain.SanankidWebAdmin:
        return SananKidsWebAdmin;
      default:
        throw new Error('Wrong domain input');
    }
  }

  public static async getRowByCaption(caption: string) {
    const page = await getSpecPage();
    const datatableRow = page.locator('datatable-body-row', {
      has: page.getByText(caption),
    });
    const htmlRow = page.locator('tr', {
      has: page.getByText(caption),
    });
    const matRow = page.locator('mat-row', {
      has: page.getByText(caption),
    });

    try {
      await Promise.all([
        datatableRow.waitFor({ timeout: 1000 }),
        htmlRow.waitFor({ timeout: 1000 }),
        matRow.waitFor({ timeout: 1000 }),
      ]);
    } catch (e) {}

    const countDatatableRow = await datatableRow.count();
    const countHtmlRow = await htmlRow.count();
    const countMatRow = await matRow.count();
    if (countDatatableRow === 1) {
      return datatableRow;
    } else if (countHtmlRow === 1) {
      return htmlRow;
    } else if (countMatRow === 1) {
      return matRow;
    }

    throw new Error('Row not found');
  }

  @Step('Wait for <text> appear')
  public async waitForTextToAppear(text: string) {
    const page = await getSpecPage();
    await waitForText(page, text);
  }

  @Step('Web: Capture screenshot')
  public async captureScreen() {
    await Gauge.captureScreenshot();
  }

  @Step('Web: => Verify on column <colName> does not have <value>')
  public async verifyColumnDoesNotHaveText(colName: string, text: string) {
    const col = await TableBase.getInstance().cellByColumnName(colName);
    await col.scrollIntoViewIfNeeded();
    await expect(col).not.toContainText(text);
  }

  @Step([
    'Web: Verify exact <text> is not visible',
    'Web: => Verify exact <text> is not visible',
  ])
  public async assertTextNotVisible(text: string) {
    const page = await getTargetLocator();
    const locator = page.getByText(text, { exact: true }).first();
    await expect(locator).not.toBeVisible();
  }
}
